# Projekt Matylda - Strategický AI Partner

Multi-agentn<PERSON> systém pro strategické poradenství a výzkum postavený na CrewAI frameworku s moderním webovým rozhraním.

## 🏗️ Architektura

Projekt je organizován do dvou hlavních částí:

- **`/backend`** - Python backend s CrewAI agenty, FastAPI server a RAG systém
- **`/frontend`** - Moderní webové rozhraní pro chat komunikaci

## 🚀 Rychlý Start

### 1. Příprava prostředí

```bash
# Klonování a přechod do adresáře
git clone <repository-url>
cd MATYLDA

# Vytvoření virtuálního prostředí
python -m venv venv
source venv/bin/activate  # Linux/Mac
# nebo
venv\Scripts\activate     # Windows
```

### 2. Konfigurace

Vytvořte `.env` soubor v root adresáři:

```env
# OpenAI API klíč (povinný)
OPENAI_API_KEY="your-openai-api-key"

# Supabase konfigurace (volitelné pro RAG)
SUPABASE_URL="your-supabase-url"
SUPABASE_KEY="your-supabase-key"

# API Server nastavení
API_HOST=0.0.0.0
API_PORT=8001
API_RELOAD=True

# Agent konfigurace
AGENT_VERBOSE=True
MAX_QUESTIONS=3
```

### 3. Instalace závislostí

```bash
# Instalace Python závislostí
cd backend
pip install -r requirements.txt
cd ..
```

### 4. Spuštění systému

#### 🚀 Automatické spuštění (doporučeno)

```bash
# Standardní spuštění obou serverů
./start_servers.sh

# Spuštění s vynuceným ukončením cizích procesů
./start_servers.sh --force

# Spuštění bez automatického otevření prohlížeče
./start_servers.sh --no-browser
```

Tento skript automaticky:
- ✅ Načte konfiguraci z `.env` souboru
- ✅ **Inteligentně detekuje vlastní vs. cizí procesy**
- ✅ Bezpečně ukončí pouze vlastní instance na portech
- ✅ **Varuje před cizími procesy** a navrhne řešení
- ✅ Spustí backend API server
- ✅ Spustí frontend HTTP server
- ✅ Ověří, že oba servery běží správně
- ✅ **Automaticky otevře prohlížeč**
- ✅ Zobrazí přístupové URL

#### 🛑 Ukončení serverů

```bash
# Ukončení obou serverů
./stop_servers.sh

# Ukončení serverů + vyčištění logů
./stop_servers.sh --clean-logs
```

#### 🔧 Manuální spuštění

Pokud preferujete manuální kontrolu:

```bash
# Backend server
cd backend
source ../venv/bin/activate
python api_server.py

# Frontend server (v novém terminálu)
cd frontend
python3 -m http.server 8080
```

### 5. Přístupové body

Po spuštění budou dostupné tyto služby:

| Služba | URL | Popis |
|--------|-----|-------|
| **🌐 Frontend** | http://localhost:8080 | Chat rozhraní |
| **🔧 Backend API** | http://localhost:8001 | REST API |
| **📖 Dokumentace** | http://localhost:8001/docs | Swagger UI |
| **🏥 Health Check** | http://localhost:8001/health | Status API |

> **💡 Tip:** Porty lze změnit v `.env` souboru (FRONTEND_PORT, API_PORT)

## 📁 Struktura projektu

```
MATYLDA/
├── backend/                    # Python backend
│   ├── api_server.py          # FastAPI server
│   ├── interactive_main.py    # Dialog logika
│   ├── rag_system.py         # RAG systém
│   ├── config_loader.py      # YAML konfigurace
│   ├── agents/               # Definice agentů
│   ├── tasks/                # Definice úkolů
│   ├── crews/                # Definice posádek
│   └── requirements.txt      # Python závislosti
├── frontend/                  # Webové rozhraní
│   ├── index.html            # Hlavní stránka
│   ├── style.css             # Styly
│   ├── chat.js               # JavaScript logika
│   └── README.md             # Frontend dokumentace
├── .env                      # Konfigurace (vytvořte)
├── VISION.md                 # Vize projektu
├── ARCHITECTURE.md           # Architektura
├── ACTION_PLAN.md           # Akční plán
└── README.md                # Tento soubor
```

## 🔧 Vývoj

### Backend

Detailní informace o backend vývoji najdete v [`backend/README.md`](backend/README.md).

Klíčové příkazy:
```bash
cd backend

# Spuštění API serveru
python api_server.py

# Interaktivní demo
python interactive_main.py

# Testy
python test_api.py
```

### Frontend

Detailní informace o frontend vývoji najdete v [`frontend/README.md`](frontend/README.md).

Frontend je jednoduchá SPA (Single Page Application) s vanilla JavaScript.

## 📖 Dokumentace

- **[VISION.md](VISION.md)** - Vize a filozofie projektu
- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Technická architektura
- **[ACTION_PLAN.md](ACTION_PLAN.md)** - Plán vývoje a milníky
- **[backend/README.md](backend/README.md)** - Backend dokumentace
- **[frontend/README.md](frontend/README.md)** - Frontend dokumentace

## 🧪 Testování

### Backend API test
```bash
cd backend
python test_api.py
```

### Manuální test přes curl
```bash
# Health check
curl http://localhost:8001/health

# Chat test
curl -X POST http://localhost:8001/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Potřebuji udělat průzkum spokojenosti"}'
```

### Frontend test
Otevřete http://localhost:8080 a vyzkoušejte chat rozhraní.

## 🚀 Produkční nasazení

### Docker (doporučeno)

```bash
# Backend
cd backend
docker build -t matylda-backend .
docker run -p 8001:8001 --env-file ../.env matylda-backend

# Frontend (nginx)
cd frontend
docker build -t matylda-frontend .
docker run -p 80:80 matylda-frontend
```

### Systemd service

Viz [`backend/README.md`](backend/README.md) pro detailní instrukce.

## 🔍 Troubleshooting

### Časté problémy

1. **Backend se nespustí**
   - Zkontrolujte OpenAI API klíč v `.env`
   - Ověřte, že port 8001 není obsazený

2. **Frontend se nepřipojí k backend**
   - Zkontrolujte, že backend běží na portu 8001
   - Ověřte CORS nastavení

3. **RAG systém nefunguje**
   - Zkontrolujte Supabase konfiguraci
   - RAG je volitelný, systém funguje i bez něj

### Logy a debugging

```bash
# Backend s debug logováním
cd backend
LOG_LEVEL=DEBUG python api_server.py

# Frontend debugging
# Otevřete Developer Tools v prohlížeči (F12)
```

## 🤝 Přispívání

1. Forkněte repository
2. Vytvořte feature branch (`git checkout -b feature/nova-funkcionalita`)
3. Commitněte změny (`git commit -am 'Přidání nové funkcionality'`)
4. Pushněte branch (`git push origin feature/nova-funkcionalita`)
5. Vytvořte Pull Request

## 📄 Licence

Tento projekt je licencován pod [MIT licencí](LICENSE).

## 📞 Kontakt

Pro otázky a podporu kontaktujte vývojový tým nebo vytvořte issue v tomto repository.
#!/bin/bash

# Matylda - Skript pro spuštění backend a frontend serverů
# Automaticky spravuje porty, ukončuje existující instance a spouští nové

set -e  # Ukončit při chybě

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkce pro výpis barevných zpráv
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "🚀 =================================="
    echo "   MATYLDA - Spuštění serverů"
    echo "==================================="
    echo -e "${NC}"
}

# Načtení .env souboru
load_env() {
    if [ -f ".env" ]; then
        print_info "Načítám konfiguraci z .env souboru..."
        # Bezpečné načtení .env souboru - pouze proměnné bez mezer v názvech
        while IFS='=' read -r key value; do
            # Přeskočit komentáře a prázdné řádky
            [[ $key =~ ^[[:space:]]*# ]] && continue
            [[ -z $key ]] && continue

            # Pouze proměnné s validními názvy (bez mezer)
            if [[ $key =~ ^[A-Za-z_][A-Za-z0-9_]*$ ]]; then
                # Odstranit uvozovky z hodnoty
                value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
                export "$key=$value"
            fi
        done < .env
        print_success "Konfigurace načtena"
    else
        print_error ".env soubor nenalezen! Zkopírujte .env.example do .env a vyplňte hodnoty."
        exit 1
    fi
}

# Funkce pro detekci vlastních procesů
is_our_process() {
    local pid=$1
    local port=$2

    # Kontrola podle PID souboru
    if [ -f "logs/backend.pid" ] && [ "$(cat logs/backend.pid 2>/dev/null)" = "$pid" ]; then
        return 0
    fi
    if [ -f "logs/frontend.pid" ] && [ "$(cat logs/frontend.pid 2>/dev/null)" = "$pid" ]; then
        return 0
    fi

    # Kontrola podle názvu procesu a argumentů
    local process_info=$(ps -p "$pid" -o comm,args --no-headers 2>/dev/null || true)

    # Backend procesy (Python s api_server.py)
    if [[ $process_info =~ python.*api_server\.py ]]; then
        return 0
    fi

    # Frontend procesy (Python HTTP server na našem portu)
    if [[ $process_info =~ python.*http\.server.*$port ]]; then
        return 0
    fi

    return 1
}

# Funkce pro ukončení procesů na portu
kill_port() {
    local port=$1
    local service_name=$2

    print_info "Kontroluji port $port pro $service_name..."

    # Najdi procesy na portu
    local pids=$(lsof -ti:$port 2>/dev/null || true)

    if [ -n "$pids" ]; then
        local our_pids=""
        local foreign_pids=""

        # Rozděl procesy na naše a cizí
        for pid in $pids; do
            if is_our_process "$pid" "$port"; then
                our_pids="$our_pids $pid"
            else
                foreign_pids="$foreign_pids $pid"
            fi
        done

        # Zpracování cizích procesů
        if [ -n "$foreign_pids" ]; then
            print_warning "⚠️  POZOR: Na portu $port běží cizí procesy: $foreign_pids"

            # Zobraz informace o cizích procesech
            for pid in $foreign_pids; do
                local process_info=$(ps -p "$pid" -o pid,comm,args --no-headers 2>/dev/null || echo "$pid <neznámý proces>")
                print_warning "   PID $pid: $process_info"
            done

            print_error "❌ CHYBA: Port $port je obsazen cizím procesem!"
            print_info "💡 Řešení:"
            print_info "   1. Změňte port v .env souboru (${service_name^^}_PORT)"
            print_info "   2. Nebo ručně ukončete cizí proces: kill $foreign_pids"
            print_info "   3. Nebo použijte --force pro vynucené ukončení"

            # Pokud není force režim, ukonči
            if [ "$FORCE_KILL" != "true" ]; then
                exit 1
            else
                print_warning "🔥 FORCE režim: Ukončuji cizí procesy..."
                echo $foreign_pids | xargs -r kill -TERM 2>/dev/null || true
                sleep 2
                local remaining=$(lsof -ti:$port 2>/dev/null || true)
                if [ -n "$remaining" ]; then
                    echo $remaining | xargs -r kill -KILL 2>/dev/null || true
                fi
            fi
        fi

        # Zpracování našich procesů
        if [ -n "$our_pids" ]; then
            print_info "Ukončuji naše procesy na portu $port: $our_pids"

            # Graceful ukončení našich procesů
            echo $our_pids | xargs -r kill -TERM 2>/dev/null || true
            sleep 2

            # Kontrola, zda naše procesy stále běží
            local remaining_our=$(lsof -ti:$port 2>/dev/null || true)
            if [ -n "$remaining_our" ]; then
                print_warning "Některé naše procesy stále běží, vynucuji ukončení..."
                echo $remaining_our | xargs -r kill -KILL 2>/dev/null || true
                sleep 1
            fi
        fi

        # Finální kontrola portu
        local final_check=$(lsof -ti:$port 2>/dev/null || true)
        if [ -z "$final_check" ]; then
            print_success "Port $port je nyní volný"
        else
            print_error "Port $port je stále obsazený!"
            return 1
        fi
    else
        print_success "Port $port je volný"
    fi
}

# Funkce pro kontrolu, zda je port dostupný
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -ti:$port >/dev/null 2>&1; then
        print_error "Port $port je stále obsazený pro $service_name!"
        return 1
    else
        print_success "Port $port je připraven pro $service_name"
        return 0
    fi
}

# Funkce pro spuštění backend serveru
start_backend() {
    print_info "Spouštím backend server..."
    
    # Kontrola virtuálního prostředí
    if [ ! -d "venv" ]; then
        print_error "Virtuální prostředí 'venv' nenalezeno!"
        print_info "Spusťte: python -m venv venv && source venv/bin/activate && pip install -r backend/requirements.txt"
        exit 1
    fi
    
    # Kontrola závislostí
    if [ ! -f "backend/requirements.txt" ]; then
        print_error "backend/requirements.txt nenalezen!"
        exit 1
    fi
    
    # Spuštění backend serveru na pozadí
    cd backend
    source ../venv/bin/activate
    
    print_info "Spouštím FastAPI server na portu $API_PORT..."
    nohup python api_server.py > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid
    cd ..
    
    # Čekání na spuštění
    print_info "Čekám na spuštění backend serveru..."
    for i in {1..30}; do
        if curl -s http://localhost:$API_PORT/health >/dev/null 2>&1; then
            print_success "Backend server běží na http://localhost:$API_PORT"
            print_success "API dokumentace: http://localhost:$API_PORT/docs"
            return 0
        fi
        sleep 1
    done
    
    print_error "Backend server se nepodařilo spustit!"
    return 1
}

# Funkce pro spuštění frontend serveru
start_frontend() {
    print_info "Spouštím frontend server..."
    
    # Spuštění frontend serveru na pozadí
    cd frontend
    print_info "Spouštím HTTP server na portu $FRONTEND_PORT..."
    nohup python3 -m http.server $FRONTEND_PORT > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../logs/frontend.pid
    cd ..
    
    # Čekání na spuštění
    print_info "Čekám na spuštění frontend serveru..."
    for i in {1..10}; do
        if curl -s http://localhost:$FRONTEND_PORT >/dev/null 2>&1; then
            print_success "Frontend server běží na http://localhost:$FRONTEND_PORT"
            return 0
        fi
        sleep 1
    done
    
    print_error "Frontend server se nepodařilo spustit!"
    return 1
}

# Funkce pro vytvoření logs adresáře
setup_logs() {
    if [ ! -d "logs" ]; then
        mkdir -p logs
        print_info "Vytvořen adresář logs/"
    fi
}

# Funkce pro otevření prohlížeče
open_browser() {
    local url="http://localhost:$FRONTEND_PORT"

    print_info "Otevírám prohlížeč na $url..."

    # Detekce operačního systému a otevření prohlížeče
    if command -v xdg-open > /dev/null; then
        # Linux
        xdg-open "$url" >/dev/null 2>&1 &
    elif command -v open > /dev/null; then
        # macOS
        open "$url" >/dev/null 2>&1 &
    elif command -v start > /dev/null; then
        # Windows (Git Bash, WSL)
        start "$url" >/dev/null 2>&1 &
    else
        print_warning "Nepodařilo se automaticky otevřít prohlížeč"
        print_info "Ručně otevřete: $url"
        return 1
    fi

    print_success "Prohlížeč otevřen"
    return 0
}

# Funkce pro zobrazení statusu
show_status() {
    echo
    print_header
    print_success "🎉 Oba servery jsou spuštěny a připraveny!"
    echo
    echo -e "${GREEN}📍 Přístupové body:${NC}"
    echo -e "   🌐 Frontend:      http://localhost:$FRONTEND_PORT"
    echo -e "   🔧 Backend API:   http://localhost:$API_PORT"
    echo -e "   📖 Dokumentace:   http://localhost:$API_PORT/docs"
    echo -e "   🏥 Health check:  http://localhost:$API_PORT/health"
    echo
    echo -e "${YELLOW}📋 Správa serverů:${NC}"
    echo -e "   🛑 Ukončení:      ./stop_servers.sh"
    echo -e "   📊 Logy:          tail -f logs/backend.log"
    echo -e "   📊 Logy:          tail -f logs/frontend.log"
    echo

    # Automatické otevření prohlížeče
    if [ "$1" != "--no-browser" ]; then
        open_browser
        echo
        echo -e "${BLUE}🌐 Prohlížeč byl automaticky otevřen!${NC}"
    else
        echo -e "${BLUE}💡 Tip: Otevřete http://localhost:$FRONTEND_PORT v prohlížeči pro testování!${NC}"
    fi
    echo
}

# Hlavní funkce
main() {
    # Zpracování parametrů
    FORCE_KILL="false"
    NO_BROWSER="false"

    for arg in "$@"; do
        case $arg in
            --force)
                FORCE_KILL="true"
                print_warning "🔥 FORCE režim aktivován - cizí procesy budou ukončeny!"
                ;;
            --no-browser)
                NO_BROWSER="true"
                ;;
            --help|-h)
                echo "Matylda - Skript pro spuštění serverů"
                echo
                echo "Použití:"
                echo "  ./start_servers.sh              Standardní spuštění"
                echo "  ./start_servers.sh --force      Vynucené ukončení cizích procesů"
                echo "  ./start_servers.sh --no-browser Neotvírat prohlížeč automaticky"
                echo "  ./start_servers.sh --help       Zobrazit tuto nápovědu"
                echo
                exit 0
                ;;
        esac
    done

    print_header

    # Načtení konfigurace
    load_env

    # Nastavení výchozích hodnot
    API_PORT=${API_PORT:-8001}
    FRONTEND_PORT=${FRONTEND_PORT:-8080}

    # Příprava
    setup_logs

    # Ukončení existujících procesů
    kill_port $API_PORT "Backend API"
    kill_port $FRONTEND_PORT "Frontend"

    # Kontrola portů
    check_port $API_PORT "Backend API" || exit 1
    check_port $FRONTEND_PORT "Frontend" || exit 1

    # Spuštění serverů
    start_backend || exit 1
    start_frontend || exit 1

    # Zobrazení statusu
    if [ "$NO_BROWSER" = "true" ]; then
        show_status --no-browser
    else
        show_status
    fi
}

# Spuštění hlavní funkce
main "$@"

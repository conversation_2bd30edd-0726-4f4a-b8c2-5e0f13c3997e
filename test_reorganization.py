#!/usr/bin/env python3
"""
Test script pro ověření úspěšné reorganizace projektové struktury
Testuje, zda všechny soubory byly správně přesunuty a struktura je funkční
"""

import os
import sys
from pathlib import Path

def test_project_structure():
    """Test základní struktury projektu"""
    print("🧪 Testování projektové struktury...")
    
    # Základní adresáře
    required_dirs = [
        "backend",
        "frontend",
        "venv"
    ]
    
    # Základní soubory v root
    required_root_files = [
        "README.md",
        "VISION.md", 
        "ARCHITECTURE.md",
        "ACTION_PLAN.md",
        ".env.example"
    ]
    
    # Backend soubory
    required_backend_files = [
        "backend/api_server.py",
        "backend/interactive_main.py",
        "backend/rag_system.py",
        "backend/config_loader.py",
        "backend/requirements.txt",
        "backend/README.md"
    ]
    
    # Backend adresáře
    required_backend_dirs = [
        "backend/agents",
        "backend/tasks", 
        "backend/crews"
    ]
    
    # Frontend soubory
    required_frontend_files = [
        "frontend/index.html",
        "frontend/style.css",
        "frontend/chat.js",
        "frontend/README.md"
    ]
    
    # Test základních adresářů
    print("\n📁 Testování základních adresářů...")
    for dir_name in required_dirs:
        if os.path.isdir(dir_name):
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ - CHYBÍ")
            return False
    
    # Test root souborů
    print("\n📄 Testování root souborů...")
    for file_name in required_root_files:
        if os.path.isfile(file_name):
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} - CHYBÍ")
            return False
    
    # Test backend souborů
    print("\n🐍 Testování backend souborů...")
    for file_name in required_backend_files:
        if os.path.isfile(file_name):
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} - CHYBÍ")
            return False
    
    # Test backend adresářů
    print("\n📂 Testování backend adresářů...")
    for dir_name in required_backend_dirs:
        if os.path.isdir(dir_name):
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ - CHYBÍ")
            return False
    
    # Test frontend souborů
    print("\n🌐 Testování frontend souborů...")
    for file_name in required_frontend_files:
        if os.path.isfile(file_name):
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} - CHYBÍ")
            return False
    
    return True

def test_backend_imports():
    """Test importů backend modulů"""
    print("\n🔧 Testování backend importů...")
    
    # Přidání backend adresáře do Python path
    backend_path = os.path.join(os.getcwd(), 'backend')
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)
    
    try:
        # Test základních importů
        import config_loader
        print("✅ config_loader import OK")
        
        import rag_system
        print("✅ rag_system import OK")
        
        import best_practices_system
        print("✅ best_practices_system import OK")
        
        # Test FastAPI serveru (bez spuštění)
        import api_server
        print("✅ api_server import OK")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_frontend_files():
    """Test obsahu frontend souborů"""
    print("\n🎨 Testování frontend obsahu...")
    
    # Test HTML souboru
    try:
        with open('frontend/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
            if 'Matylda' in html_content and 'chat.js' in html_content:
                print("✅ index.html obsahuje správné odkazy")
            else:
                print("❌ index.html - chybí klíčový obsah")
                return False
    except Exception as e:
        print(f"❌ Chyba při čtení index.html: {e}")
        return False
    
    # Test CSS souboru
    try:
        with open('frontend/style.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
            if ':root' in css_content and '--primary-color' in css_content:
                print("✅ style.css obsahuje CSS custom properties")
            else:
                print("❌ style.css - chybí CSS custom properties")
                return False
    except Exception as e:
        print(f"❌ Chyba při čtení style.css: {e}")
        return False
    
    # Test JavaScript souboru
    try:
        with open('frontend/chat.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
            if 'MatyldaChat' in js_content and '/chat' in js_content:
                print("✅ chat.js obsahuje MatyldaChat třídu a chat endpoint")
            else:
                print("❌ chat.js - chybí klíčové komponenty")
                return False
    except Exception as e:
        print(f"❌ Chyba při čtení chat.js: {e}")
        return False
    
    return True

def test_configuration():
    """Test konfiguračních souborů"""
    print("\n⚙️ Testování konfigurace...")
    
    # Test .env.example
    try:
        with open('.env.example', 'r', encoding='utf-8') as f:
            env_content = f.read()
            required_vars = ['OPENAI_API_KEY', 'API_PORT', 'SUPABASE_URL']
            missing_vars = []
            
            for var in required_vars:
                if var not in env_content:
                    missing_vars.append(var)
            
            if not missing_vars:
                print("✅ .env.example obsahuje všechny potřebné proměnné")
            else:
                print(f"❌ .env.example - chybí proměnné: {missing_vars}")
                return False
                
    except Exception as e:
        print(f"❌ Chyba při čtení .env.example: {e}")
        return False
    
    return True

def main():
    """Hlavní test funkce"""
    print("🎯 === TEST REORGANIZACE PROJEKTOVÉ STRUKTURY ===")
    print("Testování úkolu 3.1 z ACTION_PLAN.md\n")
    
    # Změna do root adresáře projektu
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    tests = [
        ("Projektová struktura", test_project_structure),
        ("Backend importy", test_backend_imports),
        ("Frontend soubory", test_frontend_files),
        ("Konfigurace", test_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"\n✅ {test_name} - ÚSPĚCH")
                passed += 1
            else:
                print(f"\n❌ {test_name} - SELHÁNÍ")
        except Exception as e:
            print(f"\n💥 {test_name} - CHYBA: {e}")
    
    # Výsledky
    print(f"\n{'='*60}")
    print(f"📊 VÝSLEDKY TESTŮ")
    print('='*60)
    print(f"✅ Úspěšné testy: {passed}/{total}")
    print(f"❌ Neúspěšné testy: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 VŠECHNY TESTY PROŠLY!")
        print("✅ Reorganizace projektové struktury byla úspěšná!")
        print("\n📋 Další kroky:")
        print("1. Spusťte backend: cd backend && python api_server.py")
        print("2. Spusťte frontend: cd frontend && python3 -m http.server 8080")
        print("3. Otevřete http://localhost:8080 v prohlížeči")
        return True
    else:
        print(f"\n⚠️  NĚKTERÉ TESTY SELHALY ({total - passed}/{total})")
        print("🔧 Zkontrolujte chyby výše a opravte je před pokračováním.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

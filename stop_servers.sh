#!/bin/bash

# Matylda - Skript pro ukončení backend a frontend serverů

set -e  # Ukončit při chybě

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkce pro výpis barevných zpráv
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${RED}"
    echo "🛑 =================================="
    echo "   MATYLDA - Ukončení serverů"
    echo "==================================="
    echo -e "${NC}"
}

# Načtení .env souboru
load_env() {
    if [ -f ".env" ]; then
        # Bezpečné načtení .env souboru - pouze proměnné bez mezer v názvech
        while IFS='=' read -r key value; do
            # Přeskočit komentáře a prázdné řádky
            [[ $key =~ ^[[:space:]]*# ]] && continue
            [[ -z $key ]] && continue

            # Pouze proměnné s validními názvy (bez mezer)
            if [[ $key =~ ^[A-Za-z_][A-Za-z0-9_]*$ ]]; then
                # Odstranit uvozovky z hodnoty
                value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
                export "$key=$value"
            fi
        done < .env
    fi
}

# Funkce pro ukončení procesů podle PID souboru
stop_by_pid() {
    local pid_file=$1
    local service_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        print_info "Ukončuji $service_name (PID: $pid)..."
        
        if kill -0 "$pid" 2>/dev/null; then
            # Graceful ukončení
            kill -TERM "$pid" 2>/dev/null || true
            sleep 2
            
            # Kontrola, zda proces stále běží
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "Proces $service_name stále běží, vynucuji ukončení..."
                kill -KILL "$pid" 2>/dev/null || true
            fi
            
            print_success "$service_name ukončen"
        else
            print_info "$service_name již neběží"
        fi
        
        rm -f "$pid_file"
    else
        print_info "PID soubor pro $service_name nenalezen"
    fi
}

# Funkce pro ukončení procesů na portu
stop_by_port() {
    local port=$1
    local service_name=$2
    
    print_info "Kontroluji procesy na portu $port ($service_name)..."
    
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        print_info "Ukončuji procesy na portu $port: $pids"
        
        # Graceful ukončení
        echo $pids | xargs -r kill -TERM 2>/dev/null || true
        sleep 2
        
        # Kontrola, zda procesy stále běží
        local remaining_pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            print_warning "Některé procesy stále běží, vynucuji ukončení..."
            echo $remaining_pids | xargs -r kill -KILL 2>/dev/null || true
        fi
        
        print_success "Procesy na portu $port ukončeny"
    else
        print_success "Žádné procesy na portu $port"
    fi
}

# Funkce pro vyčištění log souborů (volitelné)
cleanup_logs() {
    if [ "$1" = "--clean-logs" ]; then
        print_info "Čistím log soubory..."
        rm -f logs/backend.log logs/frontend.log
        print_success "Log soubory vyčištěny"
    fi
}

# Hlavní funkce
main() {
    print_header
    
    # Načtení konfigurace
    load_env
    
    # Nastavení výchozích hodnot
    API_PORT=${API_PORT:-8001}
    FRONTEND_PORT=${FRONTEND_PORT:-8080}
    
    print_info "Ukončuji Matylda servery..."
    
    # Ukončení podle PID souborů
    stop_by_pid "logs/backend.pid" "Backend API"
    stop_by_pid "logs/frontend.pid" "Frontend"
    
    # Ukončení podle portů (záložní metoda)
    stop_by_port $API_PORT "Backend API"
    stop_by_port $FRONTEND_PORT "Frontend"
    
    # Vyčištění logů pokud požadováno
    cleanup_logs "$1"
    
    echo
    print_success "🎉 Všechny servery byly ukončeny!"
    echo
    echo -e "${BLUE}💡 Pro opětovné spuštění použijte: ./start_servers.sh${NC}"
    echo
}

# Zobrazení nápovědy
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Matylda - Skript pro ukončení serverů"
    echo
    echo "Použití:"
    echo "  ./stop_servers.sh              Ukončí všechny servery"
    echo "  ./stop_servers.sh --clean-logs Ukončí servery a vyčistí logy"
    echo "  ./stop_servers.sh --help       Zobrazí tuto nápovědu"
    echo
    exit 0
fi

# Spuštění hlavní funkce
main "$1"

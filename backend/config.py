#!/usr/bin/env python3
"""
Centrální konfigurace pro projekt Matylda
Načítá všechny environment variables s defaultními hodnotami
"""

import os
from dotenv import load_dotenv

# Načtení .env souboru
load_dotenv()

class MatyldaConfig:
    """Centrální třída pro konfiguraci Matylda systému"""
    
    # === API KONFIGURACE ===
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", "8001"))
    API_RELOAD = os.getenv("API_RELOAD", "True").lower() == "true"
    API_TITLE = os.getenv("API_TITLE", "Matylda API")
    API_DESCRIPTION = os.getenv("API_DESCRIPTION", "API pro strategického AI partnera Matylda")
    API_VERSION = os.getenv("API_VERSION", "1.0.0")
    
    # === AGENT KONFIGURACE ===
    AGENT_VERBOSE = os.getenv("AGENT_VERBOSE", "True").lower() == "true"
    AGENT_ALLOW_DELEGATION = os.getenv("AGENT_ALLOW_DELEGATION", "False").lower() == "true"
    
    # === DIALOG KONFIGURACE ===
    MAX_QUESTIONS = int(os.getenv("MAX_QUESTIONS", "3"))
    CREW_VERBOSE_QUESTIONS = os.getenv("CREW_VERBOSE_QUESTIONS", "False").lower() == "true"
    CREW_VERBOSE_ANALYSIS = os.getenv("CREW_VERBOSE_ANALYSIS", "False").lower() == "true"
    
    # === DEMO KONFIGURACE ===
    DEMO_INITIAL_REQUEST = os.getenv("DEMO_INITIAL_REQUEST", "Potřebuji udělat průzkum k rekonstrukci dětského hřiště.")
    DEMO_ANSWER_1 = os.getenv("DEMO_ANSWER_1", "Chceme, aby hřiště bylo bezpečnější a zábavnější pro děti různých věkových kategorií.")
    DEMO_ANSWER_2 = os.getenv("DEMO_ANSWER_2", "Máme rozpočet kolem 500 000 Kč a chtěli bychom to stihnout do léta.")
    DEMO_ANSWER_3 = os.getenv("DEMO_ANSWER_3", "Určitě bychom chtěli zapojit rodiče i děti do rozhodování o tom, co přidat.")
    
    # === SESSION MANAGEMENT ===
    SESSION_TIMEOUT_MINUTES = int(os.getenv("SESSION_TIMEOUT_MINUTES", "60"))
    MAX_ACTIVE_SESSIONS = int(os.getenv("MAX_ACTIVE_SESSIONS", "100"))
    
    # === LOGGING ===
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT = os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    # === EXTERNAL APIS ===
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    SUPABASE_URL = os.getenv("SUPABASE_URL")
    SUPABASE_KEY = os.getenv("SUPABASE_KEY")
    
    # === DEVELOPMENT ===
    DEBUG = os.getenv("DEBUG", "True").lower() == "true"
    ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
    
    @classmethod
    def get_demo_answers(cls):
        """Vrátí seznam demo odpovědí"""
        return [
            cls.DEMO_ANSWER_1,
            cls.DEMO_ANSWER_2,
            cls.DEMO_ANSWER_3
        ]
    
    @classmethod
    def validate_config(cls):
        """Validace kritických konfigurací"""
        errors = []
        
        if not cls.OPENAI_API_KEY:
            errors.append("OPENAI_API_KEY není nastaven")
        
        if cls.MAX_QUESTIONS < 1:
            errors.append("MAX_QUESTIONS musí být alespoň 1")
        
        if cls.API_PORT < 1 or cls.API_PORT > 65535:
            errors.append("API_PORT musí být mezi 1-65535")
        
        if cls.MAX_ACTIVE_SESSIONS < 1:
            errors.append("MAX_ACTIVE_SESSIONS musí být alespoň 1")
        
        return errors
    
    @classmethod
    def print_config(cls):
        """Vypíše aktuální konfiguraci (bez citlivých dat)"""
        print("🔧 === MATYLDA KONFIGURACE ===")
        print(f"API: {cls.API_HOST}:{cls.API_PORT} (reload={cls.API_RELOAD})")
        print(f"Agenti: verbose={cls.AGENT_VERBOSE}, delegation={cls.AGENT_ALLOW_DELEGATION}")
        print(f"Dialog: max_otázky={cls.MAX_QUESTIONS}")
        print(f"Sessions: max={cls.MAX_ACTIVE_SESSIONS}, timeout={cls.SESSION_TIMEOUT_MINUTES}min")
        print(f"Prostředí: {cls.ENVIRONMENT} (debug={cls.DEBUG})")
        print(f"OpenAI API: {'✅ nastaven' if cls.OPENAI_API_KEY else '❌ chybí'}")
        print(f"Supabase: {'✅ nastaven' if cls.SUPABASE_URL else '❌ chybí'}")
        print("=" * 40)

# Globální instance konfigurace
config = MatyldaConfig()

# Validace při importu
validation_errors = config.validate_config()
if validation_errors:
    print("⚠️ CHYBY V KONFIGURACI:")
    for error in validation_errors:
        print(f"  - {error}")
    print("Zkontrolujte .env soubor!")

if __name__ == "__main__":
    # Test konfigurace
    config.print_config()
    
    errors = config.validate_config()
    if errors:
        print("\n❌ Nalezené chyby:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n✅ Konfigurace je v pořádku!")

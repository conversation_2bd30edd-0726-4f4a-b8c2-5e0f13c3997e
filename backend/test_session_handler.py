#!/usr/bin/env python3
"""
Test script pro ověření nové implementace MatyldaSession
Testuje, zda agent skutečně pokračuje v jednom dlouhodobém úkolu
"""

import os
import sys
import json
from dotenv import load_dotenv

# <PERSON>řid<PERSON>í backend adres<PERSON>ře do Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from session_handler import MatyldaSession

def test_session_continuity():
    """
    Test kontinuity session - ově<PERSON><PERSON>, že agent si pamatuje kontext
    a pokračuje v jednom dlouhodobém úkolu
    """
    print("🧪 === TEST KONTINUITY SESSION ===")
    print("Testuje, zda agent pokračuje v jednom dlouhodobém úkolu místo vytváření nových\n")
    
    # Inicializace session
    initial_request = "Potřebuji udělat průzkum spokojenosti občanů s městskými službami."
    print(f"📝 Počáteční požadavek: {initial_request}")
    
    try:
        # Vytvoření session
        session = MatyldaSession(initial_request)
        print("✅ Session vytvořena")
        
        # První otázka
        first_question = session.get_first_question()
        print(f"\n💬 První otázka: {first_question}")
        
        # Simulace odpovědí
        test_answers = [
            "Chceme zjistit, jak jsou občané spokojeni s čistotou města a kvalitou dopravy.",
            "Hlavně potřebujeme rozhodnout, zda investovat více do úklidu nebo do dopravní infrastruktury.",
            "Cílová skupina jsou všichni občané města, zejména ti, kteří denně dojíždějí do práce."
        ]
        
        # Zpracování odpovědí
        for i, answer in enumerate(test_answers, 1):
            print(f"\n👤 Odpověď {i}: {answer}")
            
            response = session.process_next_step(answer)
            print(f"🤖 Matylda: {response}")
            
            # Kontrola stavu session
            summary = session.get_session_summary()
            print(f"📊 Stav session: Iterace {summary['current_iteration']}, Dokončeno: {summary['is_complete']}")
            
            if session.is_session_complete():
                print("\n🎉 Session dokončena!")
                final_analysis = session.get_final_analysis()
                if final_analysis:
                    print("📋 Finální analýza:")
                    print(json.dumps(final_analysis, indent=2, ensure_ascii=False))
                break
        
        # Finální shrnutí
        print("\n📋 === FINÁLNÍ SHRNUTÍ SESSION ===")
        final_summary = session.get_session_summary()
        print(json.dumps(final_summary, indent=2, ensure_ascii=False))
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba během testu: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_memory():
    """
    Test paměti session - ověří, že agent si pamatuje předchozí kontext
    """
    print("\n\n🧪 === TEST PAMĚTI SESSION ===")
    print("Testuje, zda si agent pamatuje předchozí kontext mezi zprávami\n")
    
    initial_request = "Chci udělat průzkum o rekonstrukci parku."
    
    try:
        session = MatyldaSession(initial_request)
        
        # První kolo
        first_question = session.get_first_question()
        print(f"💬 První otázka: {first_question}")
        
        response1 = session.process_next_step("Park má sloužit rodinám s dětmi.")
        print(f"🤖 Odpověď 1: {response1}")
        
        # Druhé kolo - test paměti
        response2 = session.process_next_step("Rozpočet máme 2 miliony korun.")
        print(f"🤖 Odpověď 2: {response2}")
        
        # Kontrola, zda druhá odpověď odkazuje na první informaci
        if "rodin" in response2.lower() or "děti" in response2.lower() or "park" in response2.lower():
            print("✅ Agent si pamatuje předchozí kontext!")
        else:
            print("⚠️ Agent možná nepamatuje předchozí kontext")
        
        # Zobrazení historie
        summary = session.get_session_summary()
        print(f"\n📚 Historie konverzace ({len(summary['chat_history'])} zpráv):")
        for msg in summary['chat_history']:
            print(f"  {msg['type']}: {msg['content'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba během testu paměti: {e}")
        return False

def test_configuration_loading():
    """
    Test načítání konfigurace - ověří, že se správně načítají agenti a úkoly
    """
    print("\n\n🧪 === TEST NAČÍTÁNÍ KONFIGURACE ===")
    print("Testuje, zda se správně načítají konfigurační soubory\n")
    
    try:
        from config_loader import MatyldaConfigLoader
        
        loader = MatyldaConfigLoader()
        
        # Test načítání agenta
        agent = loader.create_agent("client_onboarder_v1")
        if agent:
            print("✅ Agent client_onboarder_v1 načten")
            print(f"   Role: {agent.role}")
            print(f"   Goal: {agent.goal[:100]}...")
        else:
            print("❌ Nepodařilo se načíst agenta")
            return False
        
        # Test načítání úkolu
        task = loader.create_task(
            "onboarding_master_task",
            {"client_onboarder_v1": agent},
            initial_request="Test požadavek"
        )
        if task:
            print("✅ Úkol onboarding_master_task načten")
            print(f"   Description: {task.description[:100]}...")
        else:
            print("❌ Nepodařilo se načíst úkol")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba během testu konfigurace: {e}")
        return False

def main():
    """Hlavní testovací funkce"""
    load_dotenv()
    
    print("🚀 === TESTOVÁNÍ NOVÉ IMPLEMENTACE MATYLDA SESSION ===")
    print("Cíl: Ověřit, že agent pokračuje v jednom dlouhodobém úkolu\n")
    
    # Kontrola environment variables
    required_vars = ["OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Chybí environment variables: {missing_vars}")
        print("Zkontrolujte .env soubor")
        return
    
    # Spuštění testů
    tests = [
        ("Konfigurace", test_configuration_loading),
        ("Kontinuita Session", test_session_continuity),
        ("Paměť Session", test_session_memory)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Spouštím test: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} selhal s chybou: {e}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    print(f"\n{'='*50}")
    print("SHRNUTÍ TESTŮ")
    print('='*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PROŠEL" if result else "❌ SELHAL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nVýsledek: {passed}/{len(results)} testů prošlo")
    
    if passed == len(results):
        print("🎉 Všechny testy prošly! Nová implementace funguje správně.")
    else:
        print("⚠️ Některé testy selhaly. Zkontrolujte implementaci.")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
RAG (Retrieval-Augmented Generation) systém pro Matylda
Implementuje znalostní b<PERSON> přes Supabase Vector Store
"""

import os
from typing import List, Optional
from dotenv import load_dotenv
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import SupabaseVectorStore
from supabase import create_client, Client
import logging

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MatyldaRAGSystem:
    """RAG systém pro Matylda s integrací Supabase"""
    
    def __init__(self):
        """Inicializace RAG systému"""
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        
        # Konfigurace pro Supabase tabulku
        self.table_name = os.getenv("SUPABASE_TABLE_NAME", "gdanalyst")
        self.query_name = os.getenv("SUPABASE_QUERY_NAME", "match_gdanalyst")
        
        self.supabase_client: Optional[Client] = None
        self.embeddings: Optional[OpenAIEmbeddings] = None
        self.vector_store: Optional[SupabaseVectorStore] = None
        self.retriever = None
        
        self._initialize()
    
    def _initialize(self):
        """Inicializace všech komponent RAG systému"""
        try:
            # Validace konfigurace
            if not self.supabase_url or not self.supabase_key:
                logger.warning("Supabase konfigurace není kompletní - RAG systém nebude dostupný")
                return
            
            if not self.openai_api_key:
                logger.warning("OpenAI API klíč není nastaven - RAG systém nebude dostupný")
                return
            
            # Inicializace Supabase klienta
            self.supabase_client = create_client(self.supabase_url, self.supabase_key)
            logger.info("✅ Supabase klient inicializován")
            
            # Inicializace OpenAI Embeddings
            self.embeddings = OpenAIEmbeddings(
                openai_api_key=self.openai_api_key,
                model="text-embedding-ada-002"
            )
            logger.info("✅ OpenAI Embeddings inicializovány")
            
            # Inicializace Vector Store
            self.vector_store = SupabaseVectorStore(
                client=self.supabase_client,
                embedding=self.embeddings,
                table_name=self.table_name,
                query_name=self.query_name
            )
            logger.info(f"✅ Supabase Vector Store inicializován (tabulka: {self.table_name})")
            
            # Vytvoření retrieveru
            self.retriever = self.vector_store.as_retriever(
                search_kwargs={"k": 5}  # Vrátí top 5 nejrelevantnějších dokumentů
            )
            logger.info("✅ RAG retriever připraven")
            
        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci RAG systému: {e}")
            self.retriever = None
    
    def search_knowledge_base(self, query: str) -> str:
        """
        Prohledá znalostní bázi a vrátí relevantní informace
        
        Args:
            query: Dotaz pro vyhledávání
            
        Returns:
            Formátovaný text s relevantními informacemi
        """
        if not self.retriever:
            return "❌ RAG systém není dostupný. Zkontrolujte konfiguraci Supabase a OpenAI API."
        
        try:
            logger.info(f"🔍 Vyhledávám v znalostní bázi: {query}")
            
            # Prohledání databáze
            documents = self.retriever.invoke(query)
            
            if not documents:
                return f"ℹ️ Pro dotaz '{query}' nebyly nalezeny žádné relevantní informace v znalostní bázi."
            
            # Formátování výsledků
            formatted_results = []
            for i, doc in enumerate(documents, 1):
                content = doc.page_content.strip()
                metadata = doc.metadata
                
                result = f"📄 Dokument {i}:\n{content}"
                
                # Přidání metadat pokud jsou dostupná
                if metadata:
                    if 'source' in metadata:
                        result += f"\n📍 Zdroj: {metadata['source']}"
                    if 'title' in metadata:
                        result += f"\n📝 Název: {metadata['title']}"
                
                formatted_results.append(result)
            
            final_result = f"🎯 Nalezeno {len(documents)} relevantních dokumentů pro dotaz: '{query}'\n\n"
            final_result += "\n\n---\n\n".join(formatted_results)
            
            logger.info(f"✅ Nalezeno {len(documents)} dokumentů")
            return final_result
            
        except Exception as e:
            logger.error(f"❌ Chyba při vyhledávání: {e}")
            return f"❌ Chyba při vyhledávání v znalostní bázi: {str(e)}"
    
    def is_available(self) -> bool:
        """Kontrola, zda je RAG systém dostupný"""
        return self.retriever is not None
    
    def get_status(self) -> dict:
        """Vrátí status RAG systému"""
        return {
            "available": self.is_available(),
            "supabase_configured": bool(self.supabase_url and self.supabase_key),
            "openai_configured": bool(self.openai_api_key),
            "table_name": self.table_name,
            "query_name": self.query_name
        }

# Globální instance RAG systému
rag_system = MatyldaRAGSystem()

class KnowledgeBaseSearchInput(BaseModel):
    """Input schema pro knowledge base search tool"""
    query: str = Field(..., description="Textový dotaz pro vyhledávání v znalostní bázi")

class KnowledgeBaseSearchTool(BaseTool):
    """Nástroj pro vyhledávání v znalostní bázi"""
    name: str = "knowledge_base_search"
    description: str = """
    Prohledá znalostní bázi Matylda systému a vrátí relevantní informace.

    Tento nástroj umožňuje agentům přistupovat k centrální databázi znalostí
    uložené v Supabase. Použij ho, když potřebuješ najít odborné informace,
    definice, best practices nebo odpovědi na specifické dotazy klienta.

    Příklady použití:
    - "Co je to průzkum trhu?"
    - "Jak provést analýzu konkurence?"
    - "Best practices pro dotazníkové šetření"
    """
    args_schema: type[BaseModel] = KnowledgeBaseSearchInput

    def _run(self, query: str) -> str:
        """Spustí vyhledávání v znalostní bázi"""
        return rag_system.search_knowledge_base(query)

# Instance nástroje pro použití v agentech
knowledge_base_search = KnowledgeBaseSearchTool()

def get_rag_system() -> MatyldaRAGSystem:
    """Vrátí globální instanci RAG systému"""
    return rag_system

if __name__ == "__main__":
    # Test RAG systému
    print("🧪 Testování RAG systému...")
    
    status = rag_system.get_status()
    print(f"📊 Status: {status}")
    
    if rag_system.is_available():
        print("\n🔍 Test vyhledávání...")
        test_query = "průzkum trhu"
        result = rag_system.search_knowledge_base(test_query)
        print(f"Výsledek pro '{test_query}':")
        print(result)
    else:
        print("❌ RAG systém není dostupný - zkontrolujte konfiguraci")

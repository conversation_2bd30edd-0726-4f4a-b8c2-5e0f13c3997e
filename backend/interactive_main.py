#!/usr/bin/env python3
"""
Interaktivní verze aplikace Matylda
Umožňuje postupný dialog místo jednor<PERSON>zového spuštění
"""

import os
import json
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from rag_system import knowledge_base_search, get_rag_system

class MatyldaInteractive:
    """Interaktivní třída pro postupný dialog s Matylda systémem"""

    def __init__(self):
        """Inicializace systému"""
        load_dotenv()

        # Načtení konfigurací z environment variables
        self.agent_verbose = os.getenv("AGENT_VERBOSE", "True").lower() == "true"
        self.agent_allow_delegation = os.getenv("AGENT_ALLOW_DELEGATION", "False").lower() == "true"
        self.max_questions = int(os.getenv("MAX_QUESTIONS", "3"))
        self.crew_verbose_questions = os.getenv("CREW_VERBOSE_QUESTIONS", "False").lower() == "true"
        self.crew_verbose_analysis = os.getenv("CREW_VERBOSE_ANALYSIS", "False").lower() == "true"

        # Inicializace RAG systému
        self.rag_system = get_rag_system()

        self.session_data = {}
        self.current_step = "initial"
        self.setup_agents()
    
    def setup_agents(self):
        """Nastavení agentů"""
        self.client_onboarder = Agent(
            role='Expert na vedení klientských rozhovorů a sběr zadání',
            goal='Vést efektivní a empatický dialog s klientem, abys pochopil jeho skutečné potřeby a cíle pro výzkumný projekt.',
            backstory=(
                "Jsi zkušený konzultant s praxí z top poradenských firem. "
                "Umíš klást cílené otázky, které jdou pod povrch, a aktivně nasloucháš, "
                "abys odhalil klíčové informace skryté za klientovými slovy. "
                "Když klient potřebuje vysvětlit odborný koncept nebo si není jistý terminologií, "
                "použij svůj nástroj knowledge_base_search k vyhledání relevantních informací."
            ),
            verbose=self.agent_verbose,
            allow_delegation=self.agent_allow_delegation,
            tools=[knowledge_base_search] if self.rag_system.is_available() else [],
        )
        
        self.brief_analyst = Agent(
            role='Specialista na analýzu a validaci klientských zadání',
            goal='Zkontrolovat kompletnost, srozumitelnost a strategickou relevanci informací získaných od klienta a vytvořit z nich finální, strukturované a akceschopné shrnutí.',
            backstory=(
                "Jsi precizní datový stratég. Tvojí prací je zajistit, aby každé zadání bylo "
                "křišťálově čisté, bez nejasností a přímo navázané na rozhodovací proces klienta. "
                "Tvoje shrnutí jsou legendární svou přehledností."
            ),
            verbose=self.agent_verbose,
            allow_delegation=self.agent_allow_delegation,
        )
    
    def start_conversation(self, initial_request):
        """Zahájení konverzace s počátečním požadavkem"""
        print(f"🚀 Zahajuji konverzaci s požadavkem: {initial_request}")
        
        # Uložení počátečního požadavku
        self.session_data['initial_request'] = initial_request
        self.current_step = "gathering_info"
        
        # Vytvoření úkolu pro první otázku
        first_question_task = Task(
            description=(
                f"Klient přišel s požadavkem: '{initial_request}'. "
                "Polož mu jednu cílenou otázku, která pomůže lépe pochopit jeho hlavní cíl projektu. "
                "Vrať pouze otázku, bez dalšího textu."
            ),
            expected_output='Jedna konkrétní otázka pro klienta.',
            agent=self.client_onboarder
        )
        
        # Spuštění úkolu
        crew = Crew(
            agents=[self.client_onboarder],
            tasks=[first_question_task],
            process=Process.sequential,
            verbose=self.crew_verbose_questions,
        )
        
        result = crew.kickoff()
        question = str(result).strip()
        
        print(f"💬 Matylda: {question}")
        return question
    
    def process_answer(self, user_answer):
        """Zpracování odpovědi uživatele a generování další otázky"""
        
        # Uložení odpovědi
        if 'conversation' not in self.session_data:
            self.session_data['conversation'] = []
        
        self.session_data['conversation'].append({
            'type': 'user_answer',
            'content': user_answer
        })
        
        # Rozhodnutí o dalším kroku na základě počtu otázek
        conversation_length = len(self.session_data['conversation'])
        
        if conversation_length < self.max_questions:  # Pokračuj v dotazování
            return self._generate_follow_up_question(user_answer)
        else:
            return self._finalize_conversation()
    
    def _generate_follow_up_question(self, last_answer):
        """Generování následující otázky"""
        
        conversation_context = self._build_conversation_context()
        
        follow_up_task = Task(
            description=(
                f"Na základě dosavadní konverzace: {conversation_context} "
                f"a poslední odpovědi klienta: '{last_answer}', "
                "polož další cílenou otázku, která pomůže získat více detailů "
                "o strategických rozhodnutích, která klient potřebuje učinit. "
                "Vrať pouze otázku, bez dalšího textu."
            ),
            expected_output='Jedna konkrétní následující otázka pro klienta.',
            agent=self.client_onboarder
        )
        
        crew = Crew(
            agents=[self.client_onboarder],
            tasks=[follow_up_task],
            process=Process.sequential,
            verbose=self.crew_verbose_questions,
        )
        
        result = crew.kickoff()
        question = str(result).strip()
        
        print(f"💬 Matylda: {question}")
        return question
    
    def _finalize_conversation(self):
        """Dokončení konverzace a analýza"""
        print("🔍 Analyzuji získané informace...")
        
        conversation_context = self._build_conversation_context()
        
        analysis_task = Task(
            description=(
                f"Analyzuj celou konverzaci: {conversation_context} "
                "Identifikuj a extrahuj klíčové informace: 'projekt_cil' a 'klicove_rozhodnuti'."
            ),
            expected_output='Strukturovaný výstup ve formátu JSON, který obsahuje klíče "projekt_cil" a "klicove_rozhodnuti" s extrahovanými informacemi.',
            agent=self.brief_analyst
        )
        
        crew = Crew(
            agents=[self.brief_analyst],
            tasks=[analysis_task],
            process=Process.sequential,
            verbose=self.crew_verbose_analysis,
        )
        
        result = crew.kickoff()
        
        try:
            # Pokus o parsování JSON
            analysis = json.loads(str(result))
            self.session_data['final_analysis'] = analysis
            
            print("✅ Analýza dokončena!")
            print("📊 Výsledek:")
            print(f"🎯 Cíl projektu: {analysis.get('projekt_cil', 'Neurčeno')}")
            print(f"🔑 Klíčové rozhodnutí: {analysis.get('klicove_rozhodnuti', 'Neurčeno')}")
            
            return analysis
            
        except json.JSONDecodeError:
            print("⚠️ Chyba při parsování výsledku analýzy")
            print(f"Raw výsledek: {result}")
            return {"error": "Chyba při parsování", "raw_result": str(result)}
    
    def _build_conversation_context(self):
        """Sestavení kontextu konverzace"""
        context = f"Počáteční požadavek: {self.session_data.get('initial_request', '')}\n"
        
        for i, item in enumerate(self.session_data.get('conversation', [])):
            context += f"Odpověď {i+1}: {item['content']}\n"
        
        return context
    
    def get_session_summary(self):
        """Získání shrnutí celé session"""
        return {
            'initial_request': self.session_data.get('initial_request'),
            'conversation_length': len(self.session_data.get('conversation', [])),
            'final_analysis': self.session_data.get('final_analysis'),
            'current_step': self.current_step
        }


def interactive_demo():
    """Demonstrace interaktivního dialogu"""
    print("🎭 === MATYLDA - Interaktivní Demo ===")
    print("Simulace postupného dialogu s klientem\n")
    
    # Inicializace systému
    matylda = MatyldaInteractive()
    
    # Načtení demo dat z environment variables
    initial_request = os.getenv("DEMO_INITIAL_REQUEST", "Potřebuji udělat průzkum k rekonstrukci dětského hřiště.")
    simulated_answers = [
        os.getenv("DEMO_ANSWER_1", "Chceme, aby hřiště bylo bezpečnější a zábavnější pro děti různých věkových kategorií."),
        os.getenv("DEMO_ANSWER_2", "Máme rozpočet kolem 500 000 Kč a chtěli bychom to stihnout do léta."),
        os.getenv("DEMO_ANSWER_3", "Určitě bychom chtěli zapojit rodiče i děti do rozhodování o tom, co přidat.")
    ]
    
    # Zahájení konverzace
    question = matylda.start_conversation(initial_request)
    
    # Simulace odpovědí
    for i, answer in enumerate(simulated_answers):
        print(f"👤 Klient: {answer}")
        print()
        
        if i < len(simulated_answers) - 1:
            question = matylda.process_answer(answer)
        else:
            # Poslední odpověď - dokončení
            final_result = matylda.process_answer(answer)
    
    # Shrnutí session
    print("\n📋 Shrnutí session:")
    summary = matylda.get_session_summary()
    print(json.dumps(summary, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    interactive_demo()

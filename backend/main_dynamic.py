#!/usr/bin/env python3
"""
Dynamická verze main.py používající config loader
Implementuje Milník 2.2: Dynamická Konfigurace Agentů z Externích Souborů
"""

import os
from dotenv import load_dotenv
from config_loader import get_config_loader
from rag_system import get_rag_system

# --- KROK 1: Načtení Konfigurace ---
load_dotenv()

# Načtení konfigurací z environment variables
DEMO_INITIAL_REQUEST = os.getenv("DEMO_INITIAL_REQUEST", "Potřebuji udělat průzkum k rekonstrukci dětského hřiště.")

def main():
    """Hlavní funkce pro spuštění dynamické verze Matylda systému"""
    
    print("🎭 === MATYLDA - Dynamická Verze ===")
    print("Používá konfiguraci z YAML souborů\n")
    
    # Inicializace config loaderu a RAG systému
    config_loader = get_config_loader()
    rag_system = get_rag_system()
    
    print(f"🔍 RAG systém: {'✅ dostupný' if rag_system.is_available() else '❌ nedostupný'}")
    
    # Zobrazení dostupných konfigurací
    print("\n📋 Dostupné konfigurace:")
    configs = config_loader.list_available_configs()
    for config_type, items in configs.items():
        print(f"  {config_type}: {items}")
    
    # Vytvoření a spuštění základní posádky
    print(f"\n🚀 Spouštím základní onboarding posádku...")
    print(f"📝 Vstupní požadavek: {DEMO_INITIAL_REQUEST}")
    
    try:
        # Vytvoření posádky z konfigurace
        crew = config_loader.create_crew(
            "onboarding_crew_basic", 
            initial_request=DEMO_INITIAL_REQUEST
        )
        
        if not crew:
            print("❌ Nepodařilo se vytvořit posádku")
            return
        
        print(f"✅ Posádka vytvořena s {len(crew.agents)} agenty a {len(crew.tasks)} úkoly")
        
        # Spuštění posádky
        print("\n🎬 Spouštím posádku...")
        result = crew.kickoff()
        
        print("\n\n########################")
        print("## Finální výsledek Posádky:")
        print("########################")
        print(result)
        
    except Exception as e:
        print(f"❌ Chyba při spuštění posádky: {e}")
        import traceback
        traceback.print_exc()

def test_individual_components():
    """Test jednotlivých komponent config loaderu"""
    
    print("\n🧪 === TEST JEDNOTLIVÝCH KOMPONENT ===")
    
    config_loader = get_config_loader()
    
    # Test vytvoření agenta
    print("\n🤖 Test vytvoření agenta:")
    agent = config_loader.create_agent("client_onboarder_v1")
    if agent:
        print(f"✅ Agent vytvořen: {agent.role}")
        print(f"   Nástroje: {len(agent.tools)}")
    else:
        print("❌ Nepodařilo se vytvořit agenta")
    
    # Test vytvoření druhého agenta
    print("\n📊 Test vytvoření analytika:")
    analyst = config_loader.create_agent("brief_analyst_v1")
    if analyst:
        print(f"✅ Analytik vytvořen: {analyst.role}")
        print(f"   Nástroje: {len(analyst.tools)}")
    else:
        print("❌ Nepodařilo se vytvořit analytika")
    
    # Test vytvoření úkolu
    if agent and analyst:
        print("\n📋 Test vytvoření úkolu:")
        agents = {
            "client_onboarder_v1": agent,
            "brief_analyst_v1": analyst
        }
        
        task = config_loader.create_task(
            "task_interview_v1", 
            agents, 
            initial_request=DEMO_INITIAL_REQUEST
        )
        
        if task:
            print(f"✅ Úkol vytvořen pro agenta: {task.agent.role}")
        else:
            print("❌ Nepodařilo se vytvořit úkol")

def show_config_details():
    """Zobrazí detaily konfigurací"""
    
    print("\n📖 === DETAILY KONFIGURACÍ ===")
    
    config_loader = get_config_loader()
    
    # Agenti
    print("\n🤖 AGENTI:")
    agents_config = config_loader.load_agents_config()
    for agent_id, config in agents_config.items():
        enabled = config.get('enabled', True)
        status = "✅" if enabled else "❌"
        print(f"  {status} {agent_id}: {config['role']}")
        if config.get('tools'):
            print(f"     Nástroje: {', '.join(config['tools'])}")
    
    # Úkoly
    print("\n📋 ÚKOLY:")
    tasks_config = config_loader.load_tasks_config()
    for task_id, config in tasks_config.items():
        enabled = config.get('enabled', True)
        status = "✅" if enabled else "❌"
        print(f"  {status} {task_id}: Agent {config['agent']}")
    
    # Posádky
    print("\n👥 POSÁDKY:")
    crews_config = config_loader.load_crews_config()
    for crew_id, config in crews_config.items():
        enabled = config.get('enabled', True)
        status = "✅" if enabled else "❌"
        print(f"  {status} {crew_id}: {config['name']}")
        print(f"     Agenti: {', '.join(config['agents'])}")
        print(f"     Úkoly: {', '.join(config['tasks'])}")

if __name__ == "__main__":
    # Hlavní spuštění
    main()
    
    # Dodatečné testy
    test_individual_components()
    show_config_details()
    
    print("\n🎉 Dynamická verze dokončena!")
    print("💡 Pro úpravu chování editujte YAML soubory v adresářích agents/, tasks/, crews/")

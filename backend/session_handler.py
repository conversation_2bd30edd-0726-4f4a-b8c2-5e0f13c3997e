#!/usr/bin/env python3
"""
Session Handler pro Matylda - Implementace skutečného stavového agenta
Řeší problém fragmentovaných úkolů vytvořením perzistentní instance agenta
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from config_loader import MatyldaConfigLoader
from rag_system import get_rag_system

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MatyldaSession:
    """
    Stavová třída pro udržování perzistentní instance agenta po celou dobu konverzace.
    Řeší problém, kdy se pro každou zprávu vytváří nový úkol místo pokračování v jednom dlouhodobém úkolu.
    """

    def __init__(self, initial_request: str):
        """
        Inicializace session s počátečním požadavkem.
        Vytvoří JEDNU posádku s JEDNÍM dlouhodobým úkolem, který přetrvá celou konverzaci.
        
        Args:
            initial_request: Počáteční požadavek klienta
        """
        load_dotenv()
        
        # Načtení konfigurací
        self.agent_verbose = os.getenv("AGENT_VERBOSE", "True").lower() == "true"
        self.crew_verbose = os.getenv("CREW_VERBOSE_QUESTIONS", "False").lower() == "true"
        self.max_iterations = int(os.getenv("MAX_SESSION_ITERATIONS", "20"))
        
        # Inicializace systémů
        self.config_loader = MatyldaConfigLoader()
        self.rag_system = get_rag_system()
        
        # Session data
        self.initial_request = initial_request
        self.chat_history: List[Dict[str, str]] = []
        self.current_iteration = 0
        self.is_complete = False
        self.final_analysis: Optional[Dict[str, Any]] = None
        
        # Vytvoření perzistentní posádky s dlouhodobým úkolem
        self._setup_persistent_crew()

        # Generování první otázky
        self.first_question = self._generate_first_question()

        logger.info(f"✅ MatyldaSession inicializována s požadavkem: {initial_request}")

    def _setup_persistent_crew(self):
        """
        Vytvoří JEDNU posádku s JEDNÍM dlouhodobým úkolem, který bude trvat celou konverzace.
        Toto je klíčová změna - místo vytváření nových úkolů pro každou zprávu.
        """
        try:
            # Vytvoření agenta z konfigurace
            self.agent = self.config_loader.create_agent("client_onboarder_v1")
            if not self.agent:
                raise Exception("Nepodařilo se vytvořit client_onboarder_v1 agenta")
            
            # Vytvoření JEDNOHO dlouhodobého úkolu
            self.master_task = self.config_loader.create_task(
                "onboarding_master_task",
                {"client_onboarder_v1": self.agent},
                initial_request=self.initial_request
            )
            if not self.master_task:
                raise Exception("Nepodařilo se vytvořit onboarding_master_task")
            
            # Vytvoření perzistentní posádky
            self.crew = Crew(
                agents=[self.agent],
                tasks=[self.master_task],
                process=Process.sequential,
                verbose=self.crew_verbose,
                memory=True  # Klíčové pro udržení kontextu!
            )
            
            logger.info("✅ Perzistentní posádka s dlouhodobým úkolem vytvořena")
            
        except Exception as e:
            logger.error(f"❌ Chyba při vytváření perzistentní posádky: {e}")
            raise

    def _generate_first_question(self) -> str:
        """Generuje první otázku na základě počátečního požadavku"""
        try:
            first_question_task = Task(
                description=f"""
                Klient přišel s požadavkem: '{self.initial_request}'.

                Tvým úkolem je položit jednu cílenou otázku, která pomůže lépe pochopit jeho hlavní cíl projektu.
                Postupuj podle své backstory - potvrď pochopení a polož strategickou otázku.

                Vrať pouze otázku, bez dalšího textu.
                """,
                expected_output='Jedna konkrétní otázka pro klienta.',
                agent=self.agent
            )

            first_crew = Crew(
                agents=[self.agent],
                tasks=[first_question_task],
                process=Process.sequential,
                verbose=self.crew_verbose,
                memory=True
            )

            result = first_crew.kickoff()
            return str(result).strip()

        except Exception as e:
            logger.error(f"❌ Chyba při generování první otázky: {e}")
            return "Můžete mi prosím říct více o vašem projektu a jeho hlavních cílech?"

    def get_first_question(self) -> str:
        """Vrátí první otázku pro klienta"""
        return self.first_question

    def process_next_step(self, user_input: str) -> str:
        """
        Hlavní metoda pro zpracování další zprávy od uživatele.

        KLÍČOVÁ ZMĚNA: Místo vytváření nového úkolu komunikujeme s existující posádkou
        pomocí iterativního přístupu.

        Args:
            user_input: Zpráva od uživatele

        Returns:
            Odpověď agenta (otázka nebo finální analýza)
        """
        if self.is_complete:
            return "Konverzace je již dokončena."

        if self.current_iteration >= self.max_iterations:
            logger.warning("⚠️ Dosažen maximální počet iterací, ukončujem konverzaci")
            return self._force_finalization()

        # Přidání zprávy do historie
        self.chat_history.append({
            "type": "user",
            "content": user_input,
            "iteration": self.current_iteration
        })

        try:
            # KLÍČOVÁ ZMĚNA: Iterativní komunikace s agentem
            response = self._communicate_with_agent(user_input)

            # Zpracování výsledku
            processed_response = self._process_agent_response(response)

            # Přidání odpovědi agenta do historie
            self.chat_history.append({
                "type": "agent",
                "content": processed_response,
                "iteration": self.current_iteration
            })

            self.current_iteration += 1

            logger.info(f"✅ Krok {self.current_iteration} dokončen")
            return processed_response

        except Exception as e:
            logger.error(f"❌ Chyba při zpracování kroku: {e}")
            return f"Omlouvám se, došlo k chybě: {str(e)}"

    def _communicate_with_agent(self, user_input: str) -> str:
        """
        Komunikuje s agentem v rámci dlouhodobého úkolu.

        Tato metoda implementuje skutečně iterativní přístup - agent si udržuje
        kontext svého dlouhodobého úkolu a reaguje na nové informace.
        """
        # Sestavení kontextu pro agenta
        current_context = self._build_current_context(user_input)

        # Vytvoření dočasného úkolu pro tento krok, ale v kontextu dlouhodobého cíle
        step_task = Task(
            description=self._build_step_task_description(current_context),
            expected_output=self._get_expected_output_for_current_step(),
            agent=self.agent
        )

        # Vytvoření dočasné posádky pro tento krok
        step_crew = Crew(
            agents=[self.agent],
            tasks=[step_task],
            process=Process.sequential,
            verbose=self.crew_verbose,
            memory=True  # Zachování paměti mezi kroky
        )

        # Spuštění kroku
        result = step_crew.kickoff()
        return str(result).strip()

    def _build_current_context(self, user_input: str) -> str:
        """Sestaví aktuální kontext pro agenta"""
        context = f"POČÁTEČNÍ POŽADAVEK: {self.initial_request}\n\n"
        context += "HISTORIE KONVERZACE:\n"
        
        for entry in self.chat_history:
            if entry["type"] == "user":
                context += f"KLIENT: {entry['content']}\n"
            else:
                context += f"MATYLDA: {entry['content']}\n"
        
        context += f"\nNOVÁ ZPRÁVA KLIENTA: {user_input}\n"
        context += f"ITERACE: {self.current_iteration + 1}/{self.max_iterations}\n"
        
        return context

    def _build_step_task_description(self, context: str) -> str:
        """
        Vytvoří popis úkolu pro aktuální krok v rámci dlouhodobého cíle.
        """
        return f"""
        Jsi v průběhu dlouhodobého strategického dialogu s klientem. Tvým hlavním cílem je získat kompletní zadání projektu.

        DLOUHODOBÝ CÍL:
        Získat všechny klíčové informace: 'projekt_cil', 'klicove_rozhodnuti', 'cilova_skupina', 'pozadovane_analyzy'.

        AKTUÁLNÍ SITUACE:
        {context}

        INSTRUKCE PRO TENTO KROK:
        1. Nejprve potvrď pochopení poslední odpovědi klienta (použij parafrázování)
        2. Vyhodnoť kvalitu získaných informací - jsou dostatečně konkrétní?
        3. Rozhodni o dalším kroku:
           - Pokud máš všechny potřebné informace v dostatečné kvalitě → vytvoř finální JSON brief
           - Pokud ne → polož další strategickou otázku zaměřenou na chybějící/vágní informace
        4. Buď asertivní - nepřijímaj vágní odpovědi, pomoz klientovi formulovat konkrétní cíle
        5. Používej své nástroje (knowledge_base_search) pro poskytnutí hodnoty

        FORMÁT ODPOVĚDI:
        - Pro další otázku: Pouze text otázky
        - Pro finální brief: JSON objekt s klíči projekt_cil, klicove_rozhodnuti, cilova_skupina, pozadovane_analyzy
        """

    def _get_expected_output_for_current_step(self) -> str:
        """Vrátí očekávaný výstup pro aktuální krok"""
        if self.current_iteration < 2:
            return "Jedna konkrétní, strategická otázka pro klienta s potvrzením pochopení předchozí odpovědi."
        else:
            return "Buď další strategická otázka, nebo finální JSON brief (pokud máš dostatek kvalitních informací)."

    def _process_agent_response(self, response: str) -> str:
        """
        Zpracuje odpověď agenta a rozhodne o dalším kroku.
        
        Args:
            response: Surová odpověď od agenta
            
        Returns:
            Zpracovaná odpověď pro uživatele
        """
        # Pokus o detekci finálního JSON briefu
        if self._is_final_analysis(response):
            try:
                # Extrakce JSON z odpovědi
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                
                if json_start != -1 and json_end > json_start:
                    json_str = response[json_start:json_end]
                    analysis = json.loads(json_str)
                    
                    # Validace požadovaných klíčů
                    required_keys = ['projekt_cil', 'klicove_rozhodnuti']
                    if all(key in analysis for key in required_keys):
                        self.final_analysis = analysis
                        self.is_complete = True
                        logger.info("✅ Konverzace dokončena s finální analýzou")
                        return self._format_final_response(analysis)
                
            except json.JSONDecodeError:
                logger.warning("⚠️ Nepodařilo se parsovat JSON z odpovědi agenta")
        
        # Standardní odpověď (otázka)
        return response.strip()

    def _is_final_analysis(self, response: str) -> bool:
        """Detekuje, zda odpověď obsahuje finální analýzu"""
        indicators = [
            'projekt_cil',
            'klicove_rozhodnuti',
            '"projekt_cil"',
            '"klicove_rozhodnuti"',
            'finální',
            'shrnutí',
            'analýza'
        ]
        response_lower = response.lower()
        return any(indicator in response_lower for indicator in indicators) and '{' in response

    def _format_final_response(self, analysis: Dict[str, Any]) -> str:
        """Formátuje finální odpověď pro uživatele"""
        response = "✅ Výborně! Máme kompletní zadání. Zde je shrnutí:\n\n"
        response += f"🎯 **Cíl projektu:** {analysis.get('projekt_cil', 'Neurčeno')}\n"
        response += f"🔑 **Klíčové rozhodnutí:** {analysis.get('klicove_rozhodnuti', 'Neurčeno')}\n"
        
        if 'cilova_skupina' in analysis:
            response += f"👥 **Cílová skupina:** {analysis['cilova_skupina']}\n"
        
        if 'pozadovane_analyzy' in analysis:
            response += f"📊 **Požadované analýzy:** {analysis['pozadovane_analyzy']}\n"
        
        response += "\nDěkuji za skvělou spolupráci! 🎉"
        return response

    def _force_finalization(self) -> str:
        """Vynucené ukončení při dosažení limitu iterací"""
        self.is_complete = True
        return "Dosáhli jsme maximálního počtu otázek. Pokud potřebujete pokračovat, začněte prosím novou konverzaci."

    def get_session_summary(self) -> Dict[str, Any]:
        """Vrátí shrnutí celé session"""
        return {
            'initial_request': self.initial_request,
            'chat_history': self.chat_history,
            'current_iteration': self.current_iteration,
            'is_complete': self.is_complete,
            'final_analysis': self.final_analysis,
            'total_messages': len([msg for msg in self.chat_history if msg['type'] == 'user'])
        }

    def is_session_complete(self) -> bool:
        """Vrátí True, pokud je session dokončena"""
        return self.is_complete

    def get_final_analysis(self) -> Optional[Dict[str, Any]]:
        """Vrátí finální analýzu, pokud je dostupná"""
        return self.final_analysis

import os
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from crewai_tools import ScrapeWebsiteTool, SerperDevTool
from rag_system import knowledge_base_search, get_rag_system

# --- KROK 1: Načtení Konfigurace ---
load_dotenv()

# Načtení konfigurací z environment variables
AGENT_VERBOSE = os.getenv("AGENT_VERBOSE", "True").lower() == "true"
AGENT_ALLOW_DELEGATION = os.getenv("AGENT_ALLOW_DELEGATION", "False").lower() == "true"
DEMO_INITIAL_REQUEST = os.getenv("DEMO_INITIAL_REQUEST", "Potřebuji udělat průzkum k rekonstrukci dětského hřiště.")

# Inicializace RAG systému
rag_system = get_rag_system()
print(f"🔍 RAG systém: {'✅ dostupný' if rag_system.is_available() else '❌ nedostupný'}")

# --- KROK 2: Definice Agentů (Naše "Posádka") ---

# Agent 1: Specialista na vedení rozhovoru
client_onboarder = Agent(
  role='Expert na vedení klientských rozhovorů a sběr zadání',
  goal='Vést efektivní a empatický dialog s klientem, abys pochopil jeho skutečné potřeby a cíle pro výzkumný projekt.',
  backstory=(
    "Jsi zkušený konzultant s praxí z top poradenských firem. "
    "Umíš klást cílené otázky, které jdou pod povrch, a aktivně nasloucháš, "
    "abys odhalil klíčové informace skryté za klientovými slovy. "
    "Když klient potřebuje vysvětlit odborný koncept nebo si není jistý terminologií, "
    "použij svůj nástroj knowledge_base_search k vyhledání relevantních informací."
  ),
  verbose=AGENT_VERBOSE,
  allow_delegation=AGENT_ALLOW_DELEGATION,
  tools=[knowledge_base_search] if rag_system.is_available() else []
)

# Agent 2: Specialista na analýzu zadání
brief_analyst = Agent(
  role='Specialista na analýzu a validaci klientských zadání',
  goal='Zkontrolovat kompletnost, srozumitelnost a strategickou relevanci informací získaných od klienta a vytvořit z nich finální, strukturované a akceschopné shrnutí.',
  backstory=(
    "Jsi precizní datový stratég. Tvojí prací je zajistit, aby každé zadání bylo "
    "křišťálově čisté, bez nejasností a přímo navázané na rozhodovací proces klienta. "
    "Tvoje shrnutí jsou legendární svou přehledností."
  ),
  verbose=AGENT_VERBOSE,
  allow_delegation=AGENT_ALLOW_DELEGATION,
)

# --- KROK 3: Definice Úkolů (Co má posádka udělat) ---

# Úkol 1: Vedení rozhovoru
# 'agent' parametr říká, který agent má úkol vykonat.
# 'context' můžeme použít pro předání dat z předchozích úkolů.
task_interview = Task(
  description=(
    "Veď simulovaný rozhovor s klientem, jehož prvotní požadavek je: '{initial_request}'. "
    "Začni obecnou otázkou na hlavní cíl projektu. Na základě fiktivní odpovědi se postupně doptej "
    "na klíčové strategické rozhodnutí, které potřebuje učinit. "
    "Celý přepis tohoto fiktivního dialogu bude tvým finálním výstupem."
  ),
  expected_output='Kompletní přepis fiktivního, ale realistického dialogu mezi tebou a klientem, který pokrývá minimálně cíl projektu a klíčové rozhodnutí.',
  agent=client_onboarder
)

# Úkol 2: Analýza a shrnutí
task_analysis_and_summary = Task(
  description=(
    "Vezmi přepis dialogu z předchozího úkolu. Důkladně ho zanalyzuj. "
    "Identifikuj a extrahuj klíčové informace: 'projekt_cil' a 'klicove_rozhodnuti'."
  ),
  expected_output='Strukturovaný výstup ve formátu JSON, který obsahuje klíče "projekt_cil" a "klicove_rozhodnuti" s extrahovanými informacemi. Příklad: {{"projekt_cil": "Zjistit...", "klicove_rozhodnuti": "Rozhodnout o..."}}',
  agent=brief_analyst
)

# --- KROK 4: Sestavení a Spuštění Posádky (Crew) ---

# Sestavíme posádku s našimi agenty a úkoly.
# Proces 'sequential' znamená, že úkoly se budou vykonávat jeden po druhém.
crew = Crew(
  agents=[client_onboarder, brief_analyst],
  tasks=[task_interview, task_analysis_and_summary],
  process=Process.sequential,
  verbose=AGENT_VERBOSE, # Verbose logování z konfigurace
)

# Spustíme celou operaci!
# Do 'inputs' slovníku předáme počáteční požadavek klienta.
result = crew.kickoff(inputs={'initial_request': DEMO_INITIAL_REQUEST})

print("\n\n########################")
print("## Finální výsledek Posádky:")
print("########################")
print(result)
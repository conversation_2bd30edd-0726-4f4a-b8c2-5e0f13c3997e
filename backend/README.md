# Matylda Backend

Backend systém pro Matylda - Strategický AI Partner postavený na CrewAI frameworku.

## Přehled

Backend implementuje multi-agentní systém pro strategické poradenství a výzkum. Využívá CrewAI pro orchestraci AI agentů, FastAPI pro REST API a Supabase pro perzistentní úložiště.

## Architektura

### Klíčové komponenty

- **API Server** (`api_server.py`) - FastAPI server s REST endpointy
- **Interactive Main** (`interactive_main.py`) - Hlavní logika pro postupný dialog
- **RAG System** (`rag_system.py`) - Retrieval-Augmented Generation pro znalostní bázi
- **Config Loader** (`config_loader.py`) - Dynamické načítání YAML konfigurací
- **Best Practices System** (`best_practices_system.py`) - Systém učení ze zkušeností

### <PERSON><PERSON><PERSON><PERSON><PERSON> struktura

```
backend/
├── api_server.py              # FastAPI server
├── interactive_main.py        # Hlavní dialog logika
├── rag_system.py             # RAG systém pro znalosti
├── config_loader.py          # YAML konfigurace loader
├── best_practices_system.py  # Systém best practices
├── project_learning_tool.py  # Nástroj pro učení
├── config.py                 # Základní konfigurace
├── main.py                   # Jednorázové spuštění
├── main_dynamic.py           # Dynamické spuštění
├── seed_best_practices.py    # Inicializace best practices
├── requirements.txt          # Python závislosti
├── agents/                   # YAML definice agentů
│   └── onboarding_agents.yaml
├── tasks/                    # YAML definice úkolů
│   └── onboarding_tasks.yaml
├── crews/                    # YAML definice posádek
│   └── onboarding_crew.yaml
└── test_*.py                 # Test soubory
```

## Instalace a spuštění

### Předpoklady

- Python 3.8+
- OpenAI API klíč
- Supabase instance (volitelné pro RAG)

### Instalace závislostí

```bash
cd backend
pip install -r requirements.txt
```

### Konfigurace

Vytvořte `.env` soubor v root adresáři projektu:

```env
# OpenAI API
OPENAI_API_KEY="your-openai-api-key"

# Supabase (volitelné)
SUPABASE_URL="your-supabase-url"
SUPABASE_KEY="your-supabase-key"

# API Server
API_HOST=0.0.0.0
API_PORT=8001
API_RELOAD=True

# Agent konfigurace
AGENT_VERBOSE=True
AGENT_ALLOW_DELEGATION=False
MAX_QUESTIONS=3

# RAG System
SUPABASE_TABLE_NAME=gdanalyst
RAG_SEARCH_RESULTS=5
```

### Spuštění

#### 🚀 Automatické spuštění (doporučeno)
```bash
# Z root adresáře projektu
./start_servers.sh
```

#### 🔧 Manuální spuštění API serveru
```bash
cd backend
source ../venv/bin/activate
python api_server.py
```

API bude dostupné na: http://localhost:8001 (nebo port z .env)
Dokumentace: http://localhost:8001/docs

#### ⚙️ Konfigurace portů
Porty lze změnit v `.env` souboru:
```env
API_PORT=8001
FRONTEND_PORT=8080
```

#### Interaktivní demo
```bash
cd backend
python interactive_main.py
```

#### Jednorázové spuštění
```bash
cd backend
python main.py
```

## API Endpointy

### Hlavní chat endpoint

#### POST /chat
Univerzální endpoint pro chat komunikaci.

**Request:**
```json
{
    "message": "Uživatelská zpráva",
    "session_id": "optional-session-id"
}
```

**Response:**
```json
{
    "session_id": "uuid",
    "question": "Další otázka od AI",
    "final_analysis": null,
    "status": "active",
    "is_complete": false
}
```

### Ostatní endpointy

- `GET /` - Health check
- `GET /health` - Detailní health check
- `GET /sessions` - Seznam aktivních sessions
- `GET /conversation/{session_id}/status` - Status konkrétní session
- `DELETE /conversation/{session_id}` - Smazání session

### Legacy endpointy (deprecated)

- `POST /conversation/start` - Zahájení konverzace
- `POST /conversation/answer` - Zpracování odpovědi

## Konfigurace agentů

Agenti jsou definováni v YAML souborech v adresáři `agents/`:

```yaml
agents:
  client_onboarder:
    role: "Specialista na vedení rozhovoru s klientem"
    goal: "Získat od klienta všechny potřebné informace"
    backstory: "Jsi zkušený konzultant..."
    verbose: true
    allow_delegation: false
```

## RAG Systém

RAG (Retrieval-Augmented Generation) systém poskytuje agentům přístup k znalostní bázi:

### Konfigurace
```env
SUPABASE_URL="your-supabase-url"
SUPABASE_KEY="your-supabase-key"
SUPABASE_TABLE_NAME="knowledge_table"
RAG_SEARCH_RESULTS=5
```

### Použití
```python
from rag_system import knowledge_base_search

# Vyhledání v znalostní bázi
results = knowledge_base_search("dotaz")
```

## Best Practices Systém

Systém pro ukládání a využívání osvědčených postupů:

```python
from best_practices_system import BestPracticesSystem

bp_system = BestPracticesSystem()
bp_system.save_practice("kategorie", "popis", "kontext")
practices = bp_system.get_relevant_practices("dotaz")
```

## Testování

### Spuštění testů

```bash
# Test API
cd backend
python test_api.py

# Test best practices
python test_best_practices_crew.py

# Test rozšířené posádky
python test_extended_crew.py

# Test struktury
python test_structure.py
```

### Test API s curl

```bash
# Health check
curl http://localhost:8001/health

# Chat
curl -X POST http://localhost:8001/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Potřebuji udělat průzkum"}'
```

## Vývoj

### Přidání nového agenta

1. Definujte agenta v `agents/*.yaml`
2. Vytvořte úkoly v `tasks/*.yaml`
3. Definujte posádku v `crews/*.yaml`
4. Aktualizujte `config_loader.py` pokud potřeba

### Přidání nového nástroje

1. Vytvořte funkci nástroje
2. Přidejte ji do seznamu nástrojů agenta
3. Otestujte funkcionalitu

### Debugging

```python
# Zapnutí verbose módu
AGENT_VERBOSE=True
CREW_VERBOSE_QUESTIONS=True
CREW_VERBOSE_ANALYSIS=True
```

## Produkční nasazení

### Docker (doporučeno)

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY backend/ .
RUN pip install -r requirements.txt

EXPOSE 8001
CMD ["python", "api_server.py"]
```

### Systemd service

```ini
[Unit]
Description=Matylda Backend API
After=network.target

[Service]
Type=simple
User=matylda
WorkingDirectory=/opt/matylda/backend
ExecStart=/opt/matylda/venv/bin/python api_server.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### Nginx reverse proxy

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Monitoring

- Health check endpoint: `/health`
- Logs: Standardní Python logging
- Metriky: Počet aktivních sessions v `/health`

## Bezpečnost

- API klíče v environment variables
- Input validace přes Pydantic
- Rate limiting (konfigurovatelné)
- CORS nastavení pro produkci

## Troubleshooting

### Časté problémy

1. **OpenAI API chyby**: Zkontrolujte API klíč a kredit
2. **Supabase připojení**: Ověřte URL a klíč
3. **Port konflikty**: Změňte `API_PORT` v `.env`
4. **Memory issues**: Nastavte `MAX_ACTIVE_SESSIONS`

### Logy

```bash
# Spuštění s debug logováním
LOG_LEVEL=DEBUG python api_server.py
```

# Matylda Frontend

Jednoduché webové rozhraní pro komunikaci s Matylda AI systémem.

## P<PERSON><PERSON>led

Tento frontend je navr<PERSON><PERSON> jako <PERSON> (Minimum Viable Product) pro testování a demonstraci Matylda systému. <PERSON><PERSON><PERSON><PERSON> čist<PERSON>, moderní chat rozhraní pro interakci se strategickým AI partnerem.

## Struktura souborů

```
frontend/
├── index.html      # Hlavní HTML stránka
├── style.css       # CSS styly a design
├── chat.js         # JavaScript logika pro chat
└── README.md       # Tento soubor
```

## Funkce

### 🎯 Klíčové funkce
- **Automatické session management** - Frontend automaticky spravuje session ID
- **Responzivní design** - Funguje na desktop i mobilních zařízeních
- **Real-time komunikace** - Okamžitá komunikace s backend API
- **Loading indikátory** - Vizuální feedback během zpracování
- **Error handling** - Elegantní zpra<PERSON> chyb a výpadků

### 💬 Chat funkce
- Automatické ukládání session ID
- Počítadlo znaků (limit 2000 znaků)
- Auto-resize textarea
- Enter pro odeslání (Shift+Enter pro nový řádek)
- Zobrazení strukturovaných výsledků (finální analýza)

### 🎨 UI/UX funkce
- Moderní, profesionální design
- Smooth animace a přechody
- Status indikátory (připraveno/zpracovávám/chyba)
- Loading overlay s spinner
- Responsive layout

## Spuštění

### Předpoklady
1. Backend server musí běžet (automaticky se spustí s `start_servers.sh`)
2. Moderní webový prohlížeč s podporou ES6+

### 🚀 Automatické spuštění (doporučeno)

```bash
# Z root adresáře projektu
./start_servers.sh
```

Tento skript automaticky spustí backend i frontend na správných portech.

### 🔧 Manuální spuštění

#### Možnost 1: Python HTTP server
```bash
cd frontend
python3 -m http.server 8080  # nebo port z .env
```

#### Možnost 2: Node.js HTTP server
```bash
cd frontend
npx http-server -p 8080
```

#### Možnost 3: Přímé otevření souboru
Můžete také přímo otevřít `index.html` v prohlížeči, ale kvůli CORS omezením doporučujeme použít HTTP server.

### ⚙️ Konfigurace portů

Frontend automaticky detekuje API server na základě:
1. **Konfigurace v .env** - `FRONTEND_PORT` a `API_PORT`
2. **URL parametry** - `?api_host=localhost&api_port=8001`
3. **Automatická detekce** - na základě aktuální URL

Porty lze změnit v `.env` souboru:
```env
FRONTEND_PORT=8080
API_PORT=8001
```

## Konfigurace

### API Endpoint
Frontend automaticky detekuje API endpoint na základě hostname:
- Lokální vývoj: `http://localhost:8001`
- Produkce: `http://{hostname}:8001`

Pro změnu portu nebo hostname upravte metodu `getApiBaseUrl()` v `chat.js`.

### Customizace vzhledu
Všechny barvy a styly jsou definovány jako CSS custom properties v `:root` sekci `style.css`:

```css
:root {
    --primary-color: #2563eb;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    /* ... další proměnné */
}
```

## API Komunikace

Frontend komunikuje s backend přes jediný endpoint:

### POST /chat
```json
{
    "message": "Uživatelská zpráva",
    "session_id": "optional-session-id"
}
```

**Odpověď:**
```json
{
    "session_id": "uuid",
    "question": "Další otázka od AI",
    "final_analysis": null,
    "status": "active",
    "is_complete": false
}
```

## Vývoj

### Struktura kódu

#### HTML (index.html)
- Sémantická struktura
- Accessibility atributy
- Meta tagy pro responsive design

#### CSS (style.css)
- CSS Custom Properties pro konzistentní design
- Flexbox layout
- CSS Grid pro komplexnější layouty
- Smooth animace a přechody
- Mobile-first responsive design

#### JavaScript (chat.js)
- ES6+ syntax
- Modulární třídní struktura
- Async/await pro API volání
- Error handling a user feedback
- DOM manipulation

### Rozšíření

Pro přidání nových funkcí:

1. **Nové UI komponenty**: Přidejte HTML strukturu a CSS styly
2. **API komunikace**: Rozšiřte `MatyldaChat` třídu
3. **Nové endpointy**: Přidejte metody do `chat.js`

### Debugging

Pro debugging zapněte Developer Tools v prohlížeči:
- Console pro JavaScript chyby
- Network tab pro API volání
- Elements tab pro CSS debugging

## Bezpečnost

- Všechny uživatelské vstupy jsou escapovány před zobrazením
- HTTPS doporučeno pro produkci
- API klíče nejsou vystaveny ve frontend kódu

## Budoucí vylepšení

- [ ] Offline podpora
- [ ] Push notifikace
- [ ] Více typů zpráv (obrázky, soubory)
- [ ] Tmavý režim
- [ ] Internationalizace (i18n)
- [ ] PWA funkce

## Podpora

Pro technické problémy nebo otázky kontaktujte vývojový tým nebo vytvořte issue v repository.

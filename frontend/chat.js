/**
 * Matylda Frontend Chat Logic
 * Implementuje komunikaci s backend API a řízení chat rozhraní
 */

class MatyldaChat {
    constructor() {
        this.sessionId = null;
        this.isLoading = false;
        this.apiBaseUrl = this.getApiBaseUrl();
        
        // DOM elementy
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.charCounter = document.getElementById('charCounter');
        this.sessionInfo = document.getElementById('sessionInfo');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        
        this.initializeEventListeners();
        this.updateUI();
    }

    /**
     * Získání base URL pro API na základě konfigurace
     */
    getApiBaseUrl() {
        // Použij globální konfiguraci pokud je dostupná
        if (window.frontendConfig) {
            return window.frontendConfig.getApiBaseUrl();
        }

        // Fallback pro případ, že konfigurace není dostupná
        const hostname = window.location.hostname;
        const port = hostname === 'localhost' ? '8001' : '8001';
        const protocol = window.location.protocol === 'https:' ? 'https:' : 'http:';
        return `${protocol}//${hostname}:${port}`;
    }

    /**
     * Inicializace event listenerů
     */
    initializeEventListeners() {
        // Odeslání zprávy
        this.sendButton.addEventListener('click', () => this.sendMessage());
        
        // Enter pro odeslání (Shift+Enter pro nový řádek)
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Počítadlo znaků
        this.messageInput.addEventListener('input', () => {
            this.updateCharCounter();
        });
        
        // Auto-resize textarea
        this.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });
    }

    /**
     * Aktualizace počítadla znaků
     */
    updateCharCounter() {
        const length = this.messageInput.value.length;
        this.charCounter.textContent = `${length}/2000`;
        
        if (length > 1800) {
            this.charCounter.style.color = 'var(--error-color)';
        } else if (length > 1500) {
            this.charCounter.style.color = 'var(--warning-color)';
        } else {
            this.charCounter.style.color = 'var(--text-muted)';
        }
    }

    /**
     * Auto-resize textarea
     */
    autoResizeTextarea() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 200) + 'px';
    }

    /**
     * Aktualizace UI stavů
     */
    updateUI() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = this.isLoading || !hasText;
        
        // Aktualizace session info
        if (this.sessionId) {
            this.sessionInfo.textContent = `Session: ${this.sessionId.substring(0, 8)}...`;
        } else {
            this.sessionInfo.textContent = 'Nová konverzace';
        }
        
        // Aktualizace status indikátoru
        const statusDot = this.statusIndicator.querySelector('.status-dot');
        const statusText = this.statusIndicator.querySelector('.status-text');
        
        if (this.isLoading) {
            statusDot.className = 'status-dot loading';
            statusText.textContent = 'Zpracovávám...';
        } else {
            statusDot.className = 'status-dot';
            statusText.textContent = 'Připraveno';
        }
    }

    /**
     * Zobrazení loading stavu
     */
    showLoading() {
        this.isLoading = true;
        this.loadingOverlay.classList.add('show');
        this.updateUI();
    }

    /**
     * Skrytí loading stavu
     */
    hideLoading() {
        this.isLoading = false;
        this.loadingOverlay.classList.remove('show');
        this.updateUI();
    }

    /**
     * Přidání zprávy do chatu
     */
    addMessage(content, type = 'assistant') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        if (typeof content === 'string') {
            // Jednoduchý text
            contentDiv.innerHTML = this.formatMessage(content);
        } else if (content && typeof content === 'object') {
            // Strukturovaný obsah (např. finální analýza)
            contentDiv.innerHTML = this.formatStructuredContent(content);
        }
        
        messageDiv.appendChild(contentDiv);
        this.chatMessages.appendChild(messageDiv);
        
        // Scroll na konec
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    /**
     * Formátování textové zprávy
     */
    formatMessage(text) {
        return text
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .map(line => `<p>${this.escapeHtml(line)}</p>`)
            .join('');
    }

    /**
     * Formátování strukturovaného obsahu
     */
    formatStructuredContent(content) {
        let html = '<div class="structured-content">';
        
        if (content.projekt_cil) {
            html += `<h3>🎯 Cíl projektu</h3><p>${this.escapeHtml(content.projekt_cil)}</p>`;
        }
        
        if (content.cilova_skupina) {
            html += `<h3>👥 Cílová skupina</h3><p>${this.escapeHtml(content.cilova_skupina)}</p>`;
        }
        
        if (content.klicove_otazky && Array.isArray(content.klicove_otazky)) {
            html += '<h3>❓ Klíčové otázky</h3><ul>';
            content.klicove_otazky.forEach(otazka => {
                html += `<li>${this.escapeHtml(otazka)}</li>`;
            });
            html += '</ul>';
        }
        
        if (content.doporuceni) {
            html += `<h3>💡 Doporučení</h3><p>${this.escapeHtml(content.doporuceni)}</p>`;
        }
        
        html += '</div>';
        return html;
    }

    /**
     * Escape HTML pro bezpečnost
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Odeslání zprávy
     */
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isLoading) return;
        
        // Přidání uživatelské zprávy
        this.addMessage(message, 'user');
        
        // Vyčištění inputu
        this.messageInput.value = '';
        this.updateCharCounter();
        this.autoResizeTextarea();
        
        this.showLoading();
        
        try {
            const response = await this.callChatAPI(message);
            this.handleAPIResponse(response);
        } catch (error) {
            this.handleError(error);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Volání Chat API
     */
    async callChatAPI(message) {
        const requestBody = {
            message: message
        };
        
        // Přidání session_id pokud existuje
        if (this.sessionId) {
            requestBody.session_id = this.sessionId;
        }
        
        const response = await fetch(`${this.apiBaseUrl}/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    }

    /**
     * Zpracování odpovědi z API
     */
    handleAPIResponse(response) {
        // Uložení session_id
        if (response.session_id) {
            this.sessionId = response.session_id;
        }
        
        // Zobrazení odpovědi
        if (response.question) {
            // Pokračování v konverzaci
            this.addMessage(response.question, 'assistant');
        } else if (response.final_analysis) {
            // Finální analýza
            this.addMessage('🎉 Analýza dokončena! Zde je shrnutí:', 'assistant');
            this.addMessage(response.final_analysis, 'assistant');
        } else if (response.message) {
            // Obecná zpráva
            this.addMessage(response.message, 'assistant');
        }
        
        this.updateUI();
    }

    /**
     * Zpracování chyb
     */
    handleError(error) {
        console.error('Chyba při komunikaci s API:', error);
        
        let errorMessage = 'Omlouvám se, došlo k chybě při komunikaci se serverem.';
        
        if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Nelze se připojit k serveru. Zkontrolujte, zda je backend spuštěn.';
        } else if (error.message.includes('HTTP 429')) {
            errorMessage = 'Server je momentálně přetížen. Zkuste to prosím za chvíli.';
        } else if (error.message.includes('HTTP 500')) {
            errorMessage = 'Došlo k chybě na serveru. Zkuste to prosím znovu.';
        }
        
        this.addMessage(`❌ ${errorMessage}`, 'assistant');
        
        // Aktualizace status indikátoru
        const statusDot = this.statusIndicator.querySelector('.status-dot');
        const statusText = this.statusIndicator.querySelector('.status-text');
        statusDot.className = 'status-dot error';
        statusText.textContent = 'Chyba';
        
        setTimeout(() => {
            statusDot.className = 'status-dot';
            statusText.textContent = 'Připraveno';
        }, 5000);
    }
}

// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    window.matyldaChat = new MatyldaChat();
});

/**
 * Frontend konfigurace pro Matylda
 * Načítá konfiguraci z environment variables nebo používá výchozí hodnoty
 */

class FrontendConfig {
    constructor() {
        // Výchozí konfigurace
        this.defaults = {
            API_HOST: 'localhost',
            API_PORT: '8001',
            FRONTEND_HOST: 'localhost',
            FRONTEND_PORT: '8080'
        };
        
        // Načtení konfigurace
        this.config = this.loadConfig();
    }
    
    /**
     * Načte konfiguraci z různých zdrojů
     */
    loadConfig() {
        const config = { ...this.defaults };
        
        // 1. Pokus o načtení z meta tagů (nastavených serverem)
        const metaConfig = this.loadFromMeta();
        Object.assign(config, metaConfig);
        
        // 2. Pokus o načtení z URL parametrů
        const urlConfig = this.loadFromURL();
        Object.assign(config, urlConfig);
        
        // 3. Automatická detekce na základě aktuální URL
        const autoConfig = this.autoDetectConfig();
        Object.assign(config, autoConfig);
        
        return config;
    }
    
    /**
     * Načte konfiguraci z meta tagů
     */
    loadFromMeta() {
        const config = {};
        
        const apiHost = document.querySelector('meta[name="api-host"]');
        if (apiHost) config.API_HOST = apiHost.content;
        
        const apiPort = document.querySelector('meta[name="api-port"]');
        if (apiPort) config.API_PORT = apiPort.content;
        
        return config;
    }
    
    /**
     * Načte konfiguraci z URL parametrů
     */
    loadFromURL() {
        const config = {};
        const params = new URLSearchParams(window.location.search);
        
        if (params.has('api_host')) config.API_HOST = params.get('api_host');
        if (params.has('api_port')) config.API_PORT = params.get('api_port');
        
        return config;
    }
    
    /**
     * Automatická detekce konfigurace na základě aktuální URL
     */
    autoDetectConfig() {
        const config = {};
        
        // Použij hostname z aktuální URL
        config.API_HOST = window.location.hostname;
        
        // Pro localhost použij standardní porty
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            config.API_PORT = '8001';  // Standardní backend port
        } else {
            // Pro produkci předpokládej stejný port nebo standardní HTTP/HTTPS
            config.API_PORT = window.location.port || (window.location.protocol === 'https:' ? '443' : '80');
        }
        
        return config;
    }
    
    /**
     * Vrátí kompletní API base URL
     */
    getApiBaseUrl() {
        const protocol = window.location.protocol === 'https:' ? 'https:' : 'http:';
        return `${protocol}//${this.config.API_HOST}:${this.config.API_PORT}`;
    }
    
    /**
     * Vrátí konfigurační hodnotu
     */
    get(key) {
        return this.config[key];
    }
    
    /**
     * Vrátí celou konfiguraci
     */
    getAll() {
        return { ...this.config };
    }
    
    /**
     * Debug informace o konfiguraci
     */
    debug() {
        console.group('🔧 Frontend Configuration');
        console.log('Current URL:', window.location.href);
        console.log('Detected config:', this.config);
        console.log('API Base URL:', this.getApiBaseUrl());
        console.groupEnd();
    }
}

// Globální instance konfigurace
window.frontendConfig = new FrontendConfig();

// Debug v development módu
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.frontendConfig.debug();
}

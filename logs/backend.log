nohup: ignoruji vstup
INFO:rag_system:✅ Supabase klient inicializován
INFO:rag_system:✅ OpenAI Embeddings inicializovány
INFO:rag_system:✅ Supabase Vector Store inicializován (tabulka: gdanalyst)
INFO:rag_system:✅ RAG retriever připraven
INFO:     Will watch for changes in these directories: ['/home/<USER>/vyvoj/MATYLDA/backend']
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:     Started reloader process [2796476] using WatchFiles
🚀 Spouštím Matylda API server...
📖 Dokumentace dostupná na: http://localhost:8001/docs
🔍 Health check: http://localhost:8001/health
⚙️  Konfigurace: Host=0.0.0.0, Port=8001, Reload=True
📊 Limity: Max sessions=100
INFO:rag_system:✅ Supabase klient inicializován
INFO:rag_system:✅ OpenAI Embeddings inicializovány
INFO:rag_system:✅ Supabase Vector Store inicializován (tabulka: gdanalyst)
INFO:rag_system:✅ RAG retriever připraven
INFO:     Started server process [2796565]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:43428 - "GET /health HTTP/1.1" 200 OK
🚀 Zahajuji konverzaci s požadavkem: rád bych udělal anketu
╭────────────────────────────── 🤖 Agent Started ──────────────────────────────╮
│                                                                              │
│  Agent: Expert na vedení klientských rozhovorů a sběr zadání                 │
│                                                                              │
│  Task: Klient přišel s požadavkem: 'rád bych udělal anketu'. Polož mu jednu  │
│  cílenou otázku, která pomůže lépe pochopit jeho hlavní cíl projektu. Vrať   │
│  pouze otázku, bez dalšího textu.                                            │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯

[92m02:13:29 - LiteLLM:INFO[0m: utils.py:3119 - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m02:13:30 - LiteLLM:INFO[0m: utils.py:1215 - Wrapper: Completed Call, calling success_handler
[92m02:13:30 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18
[92m02:13:30 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18
╭─────────────────────────── ✅ Agent Final Answer ────────────────────────────╮
│                                                                              │
│  Agent: Expert na vedení klientských rozhovorů a sběr zadání                 │
│                                                                              │
│  Final Answer:                                                               │
│  Jaký konkrétní cíl chcete dosáhnout pomocí této ankety?                     │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯
[92m02:13:30 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18

💬 Matylda: Jaký konkrétní cíl chcete dosáhnout pomocí této ankety?
INFO:     127.0.0.1:47278 - "POST /chat HTTP/1.1" 200 OK
╭────────────────────────────── 🤖 Agent Started ──────────────────────────────╮
│                                                                              │
│  Agent: Expert na vedení klientských rozhovorů a sběr zadání                 │
│                                                                              │
│  Task: Na základě dosavadní konverzace: Počáteční požadavek: rád bych        │
│  udělal anketu                                                               │
│  Odpověď 1: chci znát spokojenost občanů                                     │
│   a poslední odpovědi klienta: 'chci znát spokojenost občanů', polož další   │
│  cílenou otázku, která pomůže získat více detailů o strategických            │
│  rozhodnutích, která klient potřebuje učinit. Vrať pouze otázku, bez         │
│  dalšího textu.                                                              │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯

[92m02:13:47 - LiteLLM:INFO[0m: utils.py:3119 - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m02:13:48 - LiteLLM:INFO[0m: utils.py:1215 - Wrapper: Completed Call, calling success_handler
[92m02:13:48 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18
╭─────────────────────────── ✅ Agent Final Answer ────────────────────────────╮
│                                                                              │
│  Agent: Expert na vedení klientských rozhovorů a sběr zadání                 │
│                                                                              │
│  Final Answer:                                                               │
│  Jaké konkrétní aspekty spokojenosti občanů byste chtěl měřit, aby bylo      │
│  možné informovat vaše strategické rozhodnutí?                               │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯

[92m02:13:48 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18
💬 Matylda: Jaké konkrétní aspekty spokojenosti občanů byste chtěl měřit, aby bylo možné informovat vaše strategické rozhodnutí?
INFO:     127.0.0.1:40764 - "POST /chat HTTP/1.1" 200 OK
INFO:     127.0.0.1:40124 - "OPTIONS /chat HTTP/1.1" 200 OK
╭────────────────────────────── 🤖 Agent Started ──────────────────────────────╮
│                                                                              │
│  Agent: Expert na vedení klientských rozhovorů a sběr zadání                 │
│                                                                              │
│  Task: Na základě dosavadní konverzace: Počáteční požadavek: rád bych        │
│  udělal anketu                                                               │
│  Odpověď 1: chci znát spokojenost občanů                                     │
│  Odpověď 2: žádné strategické rozhodnutí nepotřebuji, jen chci vědět         │
│  témata, se kterými občané nejsou spokojeni                                  │
│   a poslední odpovědi klienta: 'žádné strategické rozhodnutí nepotřebuji,    │
│  jen chci vědět témata, se kterými občané nejsou spokojeni', polož další     │
│  cílenou otázku, která pomůže získat více detailů o strategických            │
│  rozhodnutích, která klient potřebuje učinit. Vrať pouze otázku, bez         │
│  dalšího textu.                                                              │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯

[92m02:14:49 - LiteLLM:INFO[0m: utils.py:3119 - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m02:14:51 - LiteLLM:INFO[0m: utils.py:1215 - Wrapper: Completed Call, calling success_handler
[92m02:14:51 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18
╭─────────────────────────── ✅ Agent Final Answer ────────────────────────────╮
│                                                                              │
│  Agent: Expert na vedení klientských rozhovorů a sběr zadání                 │
│                                                                              │
│  Final Answer:                                                               │
│  Jaká konkrétní témata nebo oblasti vás zajímají, pokud jde o nespokojenost  │
│  občanů?                                                                     │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯

[92m02:14:51 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18
💬 Matylda: Jaká konkrétní témata nebo oblasti vás zajímají, pokud jde o nespokojenost občanů?
INFO:     127.0.0.1:40124 - "POST /chat HTTP/1.1" 200 OK
🔍 Analyzuji získané informace...
╭────────────────────────────── 🤖 Agent Started ──────────────────────────────╮
│                                                                              │
│  Agent: Specialista na analýzu a validaci klientských zadání                 │
│                                                                              │
│  Task: Analyzuj celou konverzaci: Počáteční požadavek: rád bych udělal       │
│  anketu                                                                      │
│  Odpověď 1: chci znát spokojenost občanů                                     │
│  Odpověď 2: žádné strategické rozhodnutí nepotřebuji, jen chci vědět         │
│  témata, se kterými občané nejsou spokojeni                                  │
│  Odpověď 3: asi životní prostředí, doprava, možná další, doporuč běžné a     │
│  taková, abychom mohli srovnat spokojenost našich občanů s jinými            │
│  municipalitami                                                              │
│   Identifikuj a extrahuj klíčové informace: 'projekt_cil' a                  │
│  'klicove_rozhodnuti'.                                                       │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯

[92m02:15:05 - LiteLLM:INFO[0m: utils.py:3119 - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[92m02:15:08 - LiteLLM:INFO[0m: utils.py:1215 - Wrapper: Completed Call, calling success_handler
[92m02:15:08 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18
[92m02:15:08 - LiteLLM:INFO[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18
╭─────────────────────────── ✅ Agent Final Answer ────────────────────────────╮
│                                                                              │
│  Agent: Specialista na analýzu a validaci klientských zadání                 │
│                                                                              │
│  Final Answer:                                                               │
│  {                                                                           │
│    "projekt_cil": "Zjistit spokojenost občanů a témata, se kterými nejsou    │
│  spokojeni.",                                                                │
│    "klicove_rozhodnuti": "Není potřeba žádné strategické rozhodnutí, pouze   │
│  srovnání spokojenosti občanů s jinými municipalitami."                      │
│  }                                                                           │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯

✅ Analýza dokončena!
📊 Výsledek:
🎯 Cíl projektu: Zjistit spokojenost občanů a témata, se kterými nejsou spokojeni.
🔑 Klíčové rozhodnutí: Není potřeba žádné strategické rozhodnutí, pouze srovnání spokojenosti občanů s jinými municipalitami.
INFO:     127.0.0.1:59272 - "POST /chat HTTP/1.1" 200 OK
INFO:watchfiles.main:1 change detected

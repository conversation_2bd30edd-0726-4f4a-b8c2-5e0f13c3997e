# OpenAI API klíč pro CrewAI
OPENAI_API_KEY="your_openai_api_key_here"

# Supabase konfigurace pro databázi a RAG
SUPABASE_URL="your_supabase_url_here"
SUPABASE_KEY="your_supabase_key_here"

# Další konfigurace pro vývoj
DEBUG=True
ENVIRONMENT=development

# === MATYLDA KONFIGURACE ===

# API Server nastavení
API_HOST=0.0.0.0
API_PORT=8001
API_RELOAD=True
API_TITLE="Matylda API"
API_DESCRIPTION="API pro strategického AI partnera Matylda"
API_VERSION="1.0.0"

# Frontend Server konfigurace
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=8080

# Agent nastavení
AGENT_VERBOSE=True
AGENT_ALLOW_DELEGATION=False

# Dialog nastavení
MAX_QUESTIONS=3
CREW_VERBOSE_QUESTIONS=False
CREW_VERBOSE_ANALYSIS=False

# Session Handler nastavení
MAX_SESSION_ITERATIONS=20
CREW_VERBOSE=False

# Demo nastavení
DEMO_INITIAL_REQUEST="Potřebuji udělat průzkum k rekonstrukci dětského hřiště."
DEMO_ANSWER_1="Chceme, aby hřiště bylo bezpečnější a zábavnější pro děti různých věkových kategorií."
DEMO_ANSWER_2="Máme rozpočet kolem 500 000 Kč a chtěli bychom to stihnout do léta."
DEMO_ANSWER_3="Určitě bychom chtěli zapojit rodiče i děti do rozhodování o tom, co přidat."

# Session management
SESSION_TIMEOUT_MINUTES=60
MAX_ACTIVE_SESSIONS=100

# Logging
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# RAG System konfigurace
SUPABASE_TABLE_NAME=gdanalyst
SUPABASE_QUERY_NAME=match_gdanalyst
RAG_SEARCH_RESULTS=5
RAG_EMBEDDING_MODEL=text-embedding-ada-002
# AKČNÍ PLÁN: PROJEKT "MATYLDA"

Tento dokument popisuje klíčové milníky a kroky pro vývoj strategického AI partnera Matylda. Detailní vize a architektura jsou popsány v souborech `VISION.md` a `ARCHITECTURE.md`.

---

### ✅ FÁZE 1: Prototyp a Architektura (HOTOVO)

*   `1.1: Vytvořen základní multi-agentní systém v CrewAI.`
*   `1.2: Implementováno testovatelné API rozhraní a interaktivní skript.`
*   `1.3: Z<PERSON>žena projektová dokumentace (VISION.md, ARCHITECTURE.md).`

---

### ✅ FÁZE 2: Infrastruktura a Znalosti (HOTOVO)

*   `2.1: Implementován RAG nástroj ('knowledge_base_search') s napojením na Supabase.`
*   `2.2: Přesunuta konfigurace agentů, ú<PERSON><PERSON><PERSON> a posádek do externích YAML souborů.`
*   `2.3: Centralizována konfigurace aplikace do .env souboru.`
*   `2.4: Implementováno ukládání a využívání 'best_practices' (učení se ze zkušenosti).`

---

### ✅ FÁZE 3: Refaktoring API a Vytvoření MVP Frontendu (HOTOVO)

**Cíl:** Vytvořit robustní, čisté API a jednoduché webové rozhraní pro pohodlné testování a demonstraci.

*   **✅ Milník 3.1: Reorganizace Projektové Struktury**
    *   `✅ 3.1.1:` Vytvořit v repozitáři adresáře `/backend` a `/frontend`.
    *   `✅ 3.1.2:` Přesunout všechny existující Python soubory a konfigurační adresáře (`agents`, `tasks` atd.) do `/backend`.
    *   `✅ 3.1.3:` Vytvořit základní soubory `index.html`, `style.css`, `chat.js` v adresáři `/frontend`.

*   **✅ Milník 3.2: Refaktoring Backendového API**
    *   `✅ 3.2.1 (v api_server.py):` Zachovat kompatibilitu se starými endpointy `/conversation/start` a `/conversation/answer`.
    *   `✅ 3.2.2 (v api_server.py):` Vytvořit jediný hlavní endpoint `POST /chat`.
    *   `✅ 3.2.3 (v api_server.py):` Implementovat v `/chat` logiku, která na základě přítomnosti `session_id` v requestu buď založí novou session, nebo pokračuje ve stávající.

*   **✅ Milník 3.3: Implementace MVP Chat Frontendu**
    *   `✅ 3.3.1 (v chat.js):` Implementovat logiku pro udržování `session_id` na straně klienta.
    *   `✅ 3.3.2 (v chat.js):` Implementovat funkci pro odesílání požadavků na `POST /chat` endpoint.
    *   `✅ 3.3.3 (v index.html a style.css):` Vytvořit minimální vizuální rozhraní pro chat.
    *   `✅ 3.3.4:` Otestovat kompletní end-to-end komunikaci mezi novým frontendem a refaktorovaným backendem.

---

### 🌟 FÁZE 4: Rozšíření Ekosystému a Autonomie (BUDOUCNOST)

*   **Milník 4.1: Optimalizace Zdrojů (LLM Router)**
    *   `4.1.1:` Implementovat "LLM Router", který dynamicky vybírá model (levný/rychlý vs. drahý/chytrý).
    *   `4.1.2:` Přidat základní logování nákladů na LLM volání.

*   **Milník 4.2: Rozšíření Týmu Specialistů**
    *   `4.2.1:` Přidat do systému agenty `SurveyArchitect` a `QualitySupervisor` s jejich úkoly.
    *   `4.2.2:` Implementovat nové nástroje (`generate_lss_tool` atd.).
    *   `4.2.3:` Vytvořit a otestovat novou, rozšířenou posádku `Full Research Project Crew`.

*   **Milník 4.3: Samoobjevování a Plná Autonomie**
    *   `4.3.1:` Vytvořit a naplnit tabulku `agent_registry` v Supabase.
    *   `4.3.2:` Implementovat nástroj `find_agent` pro dynamické vyhledávání agentů.
    *   `4.3.3:` Implementovat hlavního orchestrátora `MatyldaOrchestrator`, který využívá registr pro delegování úkolů.
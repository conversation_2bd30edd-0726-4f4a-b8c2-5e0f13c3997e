### **Úkol: Zadání pro Implementaci Human-in-the-Loop (HITL)**

<PERSON><PERSON> je detail<PERSON>, který můžete uložit a zadat kódérovi, až přijde správný čas.

#### **Detailní Zadání: Milník 5.1 - Implementace Schvalovacího Procesu (Human-in-the-Loop)**

**Cíl:** Upravit agenta `QualitySupervisor` a systém tak, aby nenavrhoval změny autonomně, ale vytvářel "návrhy na změnu", které musí schválit lidský operátor.

**Stav:** **K IMPLEMENTACI (Budoucnost)**

**Role:** Vy (Stratég & Kódér) s podporou AI Kódéra.

**Předpoklady:** Fáze učení ze zkušeností je implementována.

**Detailní zadání pro Kódéra:**

1.  **Vytvoření Tabulky pro Návrhy (`change_proposals` v Supabase):**
    *   **Sloupce:** `id` (uuid), `created_at` (timestamp), `proposing_agent_id` (text), `target_file` (text, např. `/agents/onboarding_agents.yaml`), `target_key` (text, např. `client_onboarder_v1.backstory`), `reasoning` (text, proč je změna navrhována), `proposed_content` (text, nový navrhovaný obsah), `status` (text, výchozí hodnota 'pending_approval').

2.  **Refaktoring Nástroje `save_best_practice`:**
    *   Přejmenovat nástroj na `propose_best_practice_update`.
    *   Místo přímého zápisu do tabulky `best_practices` bude tento nástroj vytvářet nový záznam v tabulce `change_proposals`.

3.  **Vytvoření Nového Komunikačního Nástroje:**
    *   Implementovat nástroj `notify_human_operator(message: str)`.
    *   Tento nástroj odešle notifikaci na předem definovaný kanál (pro MVP stačí, když bude zprávu logovat do konzole s prefixem `[HUMAN ALERT]`, v budoucnu to může být email, Slack, atd.).

4.  **Aktualizace Úkolu `learn_from_project_outcome`:**
    *   Upravit `description` tak, aby agent `QualitySupervisor` po analýze projektu:
        1.  Použil nástroj `propose_best_practice_update` k vytvoření návrhu na změnu.
        2.  Použil nástroj `notify_human_operator` k odeslání zprávy: "Byl vytvořen nový návrh na vylepšení. Prosím o revizi a schválení v administraci."

5.  **Vytvoření Schvalovacího Skriptu/Endpointu:**
    *   Vytvořit jednoduchý skript (nebo API endpoint, např. `POST /proposals/{proposal_id}/approve`), který lidský operátor může spustit.
    *   Tento skript načte návrh z `change_proposals`, provede skutečnou úpravu v příslušném `.yaml` souboru, a změní status návrhu na `approved`.

---

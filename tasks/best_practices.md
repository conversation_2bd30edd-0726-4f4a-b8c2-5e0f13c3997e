### **Zadání pro AI Kódéra: Refaktoring Ukládání "Best Practices"**

**PROJEKT:** Matylda
**ÚKOL:** Přepracovat stávající mechanismus ukládání "Best Practices" z lokálních YAML souborů na robustní, sémanticky prohledávatelné řešení v naší centrální databázi Supabase.

**Problém k vyřešení:**
Současná implementace ukládá zkušenosti do lokálních souborů. Toto řešení není <PERSON>, neumožňuje efektivní vyhledávání a není dostupné pro všechny instance naší aplikace v produkčním prostředí.

**Cíl:**
Vytvořit perzistentní, centralizovanou a inteligentní "institucionální paměť" pro systém Matylda.

---

### **DETAILNÍ TECHNICKÁ SPECIFIKACE**

#### **ČÁST 1: PŘÍPRAVA DATABÁZE (SUPABASE)**

1.  **Vytvoření Tabulky:**
    *   V naší existující Supabase databázi vytvoř novou tabulku.
    *   **Název tabulky:** `best_practices`
    *   **Specifikace sloupců:**
        *   `id`: `uuid` (Primary Key, výchozí hodnota `gen_random_uuid()`)
        *   `created_at`: `timestamptz` (Výchozí hodnota `now()`)
        *   `context_description`: `text` (Popis situace, která vedla k této zkušenosti. Tento sloupec bude vektorizován.)
        *   `successful_strategy`: `text` (Popis ověřeného postupu nebo řešení.)
        *   `associated_agent_role`: `text` (Role agenta, např. `ClientOnboarder`.)
        *   `success_rating`: `float4` (Číselné hodnocení úspěšnosti, např. 0.95.)
        *   `feedback_notes`: `text[]` (Pole textů pro dodatečné poznámky.)
        *   `embedding`: `vector` (Velikost vektoru musí odpovídat modelu, který používáme, např. 1536 pro `text-embedding-ada-002`.)
    *   **Zapnutí RLS:** Povol Row Level Security pro tuto tabulku.

2.  **Vytvoření Databázové Funkce pro Vyhledávání:**
    *   Vytvoř v Supabase novou SQL funkci pro sémantické vyhledávání.
    *   **Název funkce:** `match_best_practices`
    *   **Definice funkce:**
        ```sql
        CREATE OR REPLACE FUNCTION match_best_practices (
          query_embedding vector(1536),
          match_threshold float,
          match_count int
        )
        RETURNS TABLE (
          id uuid,
          context_description text,
          successful_strategy text,
          associated_agent_role text,
          similarity float
        )
        LANGUAGE plpgsql
        AS $$
        BEGIN
          RETURN QUERY
          SELECT
            bp.id,
            bp.context_description,
            bp.successful_strategy,
            bp.associated_agent_role,
            1 - (bp.embedding <=> query_embedding) as similarity
          FROM
            best_practices bp
          WHERE 1 - (bp.embedding <=> query_embedding) > match_threshold
          ORDER BY
            similarity DESC
          LIMIT match_count;
        END;
        $$;
        ```

#### **ČÁST 2: IMPLEMENTACE NÁSTROJŮ V PYTHONU (`tools/best_practices_tools.py`)**

Vytvoř nový soubor pro nástroje související s best practices, aby byl kód modulární.

1.  **Nástroj `save_best_practice`:**
    *   **Dekorátor:** `@tool("save_best_practice")`
    *   **Vstupní argumenty:** `context_description: str`, `successful_strategy: str`, `associated_agent_role: str`, `feedback_notes: list[str]`, `success_rating: float = 0.9`.
    *   **Vnitřní logika:**
        1.  Získá inicializovaného Supabase klienta a OpenAI klienta z globálního kontextu nebo přes dependency injection.
        2.  Vytvoří vektorový embedding pro vstupní `context_description` pomocí OpenAI API.
        3.  Provede operaci `INSERT` do tabulky `best_practices` v Supabase se všemi poskytnutými daty.
        4.  Vrátí potvrzovací zprávu, např. `f"Nová zkušenost úspěšně uložena s ID: {nové_id}"`.
        5.  Implementuj robustní `try...except` blok pro ošetření chyb při komunikaci s databází.

2.  **Nástroj `find_relevant_best_practice`:**
    *   **Dekorátor:** `@tool("find_relevant_best_practice")`
    *   **Vstupní argumenty:** `current_situation_description: str`, `match_count: int = 2`, `match_threshold: float = 0.75`.
    *   **Vnitřní logika:**
        1.  Získá inicializovaného Supabase klienta a OpenAI klienta.
        2.  Vytvoří vektorový embedding pro vstupní `current_situation_description`.
        3.  Zavolá databázovou funkci `match_best_practices` přes Supabase klienta (`supabase.rpc(...)`).
        4.  Pokud funkce vrátí nějaké výsledky, zformátuje je do jednoho přehledného textového řetězce.
            *   Příklad formátu: `Nalezeny relevantní zkušenosti:\n\nZKUŠENOST 1:\nSituace: [context_description]\nDoporučený postup: [successful_strategy]\n---\nZKUŠENOST 2:\n...`
        5.  Pokud funkce nic nevrátí, vrátí zprávu: `"Nebyly nalezeny žádné relevantní zkušenosti z minulosti."`
        6.  Implementuj `try...except` blok.

#### **ČÁST 3: INTEGRACE DO SYSTÉMU**

1.  **Konfigurace Nástrojů:**
    *   V `main.py` (nebo v `factory.py`) importuj tyto nové nástroje a přidej je do slovníku `available_tools`.

2.  **Aktualizace Konfigurace Agentů (`agents.yaml`):**
    *   U agentů `QualitySupervisor`, `ClientOnboarder` a `SurveyArchitect` přidej do jejich `tools` seznamu názvy nových nástrojů (`save_best_practice`, `find_relevant_best_practice`) podle potřeby a podle naší předchozí diskuze. Ujisti se, že `QualitySupervisor` má `save_best_practice`.

3.  **Aktualizace Úkolů (`tasks.yaml`):**
    *   Zkontroluj a případně uprav `description` a `expected_output` pro úkol `learn_from_project_outcome`, aby explicitně instruoval `QualitySupervisor` agenta k použití nástroje `save_best_practice` s daty z dokončené konverzace.

**FINÁLNÍ KONTROLA:** Po dokončení implementace musí být veškerá logika pro práci s "best practices" plně řízena komunikací s databází Supabase. Z projektu musí zmizet jakékoli čtení nebo zápis do lokálních YAML souborů pro tento účel.